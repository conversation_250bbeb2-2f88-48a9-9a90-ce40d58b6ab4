(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function nr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ye={},vn=[],at=()=>{},_c=()=>!1,Vs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),sr=e=>e.startsWith("onUpdate:"),Ie=Object.assign,or=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Cc=Object.prototype.hasOwnProperty,pe=(e,t)=>Cc.call(e,t),J=Array.isArray,bn=e=>os(e)==="[object Map]",Bs=e=>os(e)==="[object Set]",Dr=e=>os(e)==="[object Date]",te=e=>typeof e=="function",Oe=e=>typeof e=="string",wt=e=>typeof e=="symbol",ve=e=>e!==null&&typeof e=="object",na=e=>(ve(e)||te(e))&&te(e.then)&&te(e.catch),sa=Object.prototype.toString,os=e=>sa.call(e),xc=e=>os(e).slice(8,-1),oa=e=>os(e)==="[object Object]",rr=e=>Oe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Hn=nr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),js=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ec=/-(\w)/g,ot=js(e=>e.replace(Ec,(t,n)=>n?n.toUpperCase():"")),Rc=/\B([A-Z])/g,Qt=js(e=>e.replace(Rc,"-$1").toLowerCase()),Gs=js(e=>e.charAt(0).toUpperCase()+e.slice(1)),fo=js(e=>e?`on${Gs(e)}`:""),qt=(e,t)=>!Object.is(e,t),ms=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ko=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Lo=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Tc=e=>{const t=Oe(e)?Number(e):NaN;return isNaN(t)?e:t};let Mr;const $s=()=>Mr||(Mr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function _t(e){if(J(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=Oe(s)?Oc(s):_t(s);if(o)for(const r in o)t[r]=o[r]}return t}else if(Oe(e)||ve(e))return e}const Ac=/;(?![^(]*\))/g,Pc=/:([^]+)/,Ic=/\/\*[^]*?\*\//g;function Oc(e){const t={};return e.replace(Ic,"").split(Ac).forEach(n=>{if(n){const s=n.split(Pc);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function We(e){let t="";if(Oe(e))t=e;else if(J(e))for(let n=0;n<e.length;n++){const s=We(e[n]);s&&(t+=s+" ")}else if(ve(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const kc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Lc=nr(kc);function ra(e){return!!e||e===""}function Nc(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Ks(e[s],t[s]);return n}function Ks(e,t){if(e===t)return!0;let n=Dr(e),s=Dr(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=wt(e),s=wt(t),n||s)return e===t;if(n=J(e),s=J(t),n||s)return n&&s?Nc(e,t):!1;if(n=ve(e),s=ve(t),n||s){if(!n||!s)return!1;const o=Object.keys(e).length,r=Object.keys(t).length;if(o!==r)return!1;for(const i in e){const a=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(a&&!l||!a&&l||!Ks(e[i],t[i]))return!1}}return String(e)===String(t)}function ia(e,t){return e.findIndex(n=>Ks(n,t))}const aa=e=>!!(e&&e.__v_isRef===!0),se=e=>Oe(e)?e:e==null?"":J(e)||ve(e)&&(e.toString===sa||!te(e.toString))?aa(e)?se(e.value):JSON.stringify(e,la,2):String(e),la=(e,t)=>aa(t)?la(e,t.value):bn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],r)=>(n[ho(s,r)+" =>"]=o,n),{})}:Bs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ho(n))}:wt(t)?ho(t):ve(t)&&!J(t)&&!oa(t)?String(t):t,ho=(e,t="")=>{var n;return wt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let He;class ca{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=He,!t&&He&&(this.index=(He.scopes||(He.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=He;try{return He=this,t()}finally{He=n}}}on(){++this._on===1&&(this.prevScope=He,He=this)}off(){this._on>0&&--this._on===0&&(He=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function ua(e){return new ca(e)}function fa(){return He}function Dc(e,t=!1){He&&He.cleanups.push(e)}let Se;const po=new WeakSet;class da{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,He&&He.active&&He.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,po.has(this)&&(po.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||pa(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Hr(this),ma(this);const t=Se,n=lt;Se=this,lt=!0;try{return this.fn()}finally{ga(this),Se=t,lt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)lr(t);this.deps=this.depsTail=void 0,Hr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?po.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){No(this)&&this.run()}get dirty(){return No(this)}}let ha=0,Fn,Un;function pa(e,t=!1){if(e.flags|=8,t){e.next=Un,Un=e;return}e.next=Fn,Fn=e}function ir(){ha++}function ar(){if(--ha>0)return;if(Un){let t=Un;for(Un=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Fn;){let t=Fn;for(Fn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function ma(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ga(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),lr(s),Mc(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function No(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ya(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ya(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Jn)||(e.globalVersion=Jn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!No(e))))return;e.flags|=2;const t=e.dep,n=Se,s=lt;Se=e,lt=!0;try{ma(e);const o=e.fn(e._value);(t.version===0||qt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{Se=n,lt=s,ga(e),e.flags&=-3}}function lr(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)lr(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Mc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let lt=!0;const va=[];function Ot(){va.push(lt),lt=!1}function kt(){const e=va.pop();lt=e===void 0?!0:e}function Hr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Se;Se=void 0;try{t()}finally{Se=n}}}let Jn=0;class Hc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class cr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Se||!lt||Se===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Se)n=this.activeLink=new Hc(Se,this),Se.deps?(n.prevDep=Se.depsTail,Se.depsTail.nextDep=n,Se.depsTail=n):Se.deps=Se.depsTail=n,ba(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=Se.depsTail,n.nextDep=void 0,Se.depsTail.nextDep=n,Se.depsTail=n,Se.deps===n&&(Se.deps=s)}return n}trigger(t){this.version++,Jn++,this.notify(t)}notify(t){ir();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ar()}}}function ba(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)ba(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Es=new WeakMap,ln=Symbol(""),Do=Symbol(""),Xn=Symbol("");function Fe(e,t,n){if(lt&&Se){let s=Es.get(e);s||Es.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new cr),o.map=s,o.key=n),o.track()}}function Tt(e,t,n,s,o,r){const i=Es.get(e);if(!i){Jn++;return}const a=l=>{l&&l.trigger()};if(ir(),t==="clear")i.forEach(a);else{const l=J(e),u=l&&rr(n);if(l&&n==="length"){const c=Number(s);i.forEach((f,p)=>{(p==="length"||p===Xn||!wt(p)&&p>=c)&&a(f)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),u&&a(i.get(Xn)),t){case"add":l?u&&a(i.get("length")):(a(i.get(ln)),bn(e)&&a(i.get(Do)));break;case"delete":l||(a(i.get(ln)),bn(e)&&a(i.get(Do)));break;case"set":bn(e)&&a(i.get(ln));break}}ar()}function Fc(e,t){const n=Es.get(e);return n&&n.get(t)}function hn(e){const t=ue(e);return t===e?t:(Fe(t,"iterate",Xn),tt(e)?t:t.map(Ne))}function Ws(e){return Fe(e=ue(e),"iterate",Xn),e}const Uc={__proto__:null,[Symbol.iterator](){return mo(this,Symbol.iterator,Ne)},concat(...e){return hn(this).concat(...e.map(t=>J(t)?hn(t):t))},entries(){return mo(this,"entries",e=>(e[1]=Ne(e[1]),e))},every(e,t){return Ct(this,"every",e,t,void 0,arguments)},filter(e,t){return Ct(this,"filter",e,t,n=>n.map(Ne),arguments)},find(e,t){return Ct(this,"find",e,t,Ne,arguments)},findIndex(e,t){return Ct(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ct(this,"findLast",e,t,Ne,arguments)},findLastIndex(e,t){return Ct(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ct(this,"forEach",e,t,void 0,arguments)},includes(...e){return go(this,"includes",e)},indexOf(...e){return go(this,"indexOf",e)},join(e){return hn(this).join(e)},lastIndexOf(...e){return go(this,"lastIndexOf",e)},map(e,t){return Ct(this,"map",e,t,void 0,arguments)},pop(){return In(this,"pop")},push(...e){return In(this,"push",e)},reduce(e,...t){return Fr(this,"reduce",e,t)},reduceRight(e,...t){return Fr(this,"reduceRight",e,t)},shift(){return In(this,"shift")},some(e,t){return Ct(this,"some",e,t,void 0,arguments)},splice(...e){return In(this,"splice",e)},toReversed(){return hn(this).toReversed()},toSorted(e){return hn(this).toSorted(e)},toSpliced(...e){return hn(this).toSpliced(...e)},unshift(...e){return In(this,"unshift",e)},values(){return mo(this,"values",Ne)}};function mo(e,t,n){const s=Ws(e),o=s[t]();return s!==e&&!tt(e)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=n(r.value)),r}),o}const Vc=Array.prototype;function Ct(e,t,n,s,o,r){const i=Ws(e),a=i!==e&&!tt(e),l=i[t];if(l!==Vc[t]){const f=l.apply(e,r);return a?Ne(f):f}let u=n;i!==e&&(a?u=function(f,p){return n.call(this,Ne(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const c=l.call(i,u,s);return a&&o?o(c):c}function Fr(e,t,n,s){const o=Ws(e);let r=n;return o!==e&&(tt(e)?n.length>3&&(r=function(i,a,l){return n.call(this,i,a,l,e)}):r=function(i,a,l){return n.call(this,i,Ne(a),l,e)}),o[t](r,...s)}function go(e,t,n){const s=ue(e);Fe(s,"iterate",Xn);const o=s[t](...n);return(o===-1||o===!1)&&dr(n[0])?(n[0]=ue(n[0]),s[t](...n)):o}function In(e,t,n=[]){Ot(),ir();const s=ue(e)[t].apply(e,n);return ar(),kt(),s}const Bc=nr("__proto__,__v_isRef,__isVue"),Sa=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(wt));function jc(e){wt(e)||(e=String(e));const t=ue(this);return Fe(t,"has",e),t.hasOwnProperty(e)}class wa{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(o?r?Qc:Ea:r?xa:Ca).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=J(t);if(!o){let l;if(i&&(l=Uc[n]))return l;if(n==="hasOwnProperty")return jc}const a=Reflect.get(t,n,Pe(t)?t:s);return(wt(n)?Sa.has(n):Bc(n))||(o||Fe(t,"get",n),r)?a:Pe(a)?i&&rr(n)?a:a.value:ve(a)?o?Ta(a):rs(a):a}}class _a extends wa{constructor(t=!1){super(!1,t)}set(t,n,s,o){let r=t[n];if(!this._isShallow){const l=Xt(r);if(!tt(s)&&!Xt(s)&&(r=ue(r),s=ue(s)),!J(t)&&Pe(r)&&!Pe(s))return l?!1:(r.value=s,!0)}const i=J(t)&&rr(n)?Number(n)<t.length:pe(t,n),a=Reflect.set(t,n,s,Pe(t)?t:o);return t===ue(o)&&(i?qt(s,r)&&Tt(t,"set",n,s):Tt(t,"add",n,s)),a}deleteProperty(t,n){const s=pe(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&Tt(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!wt(n)||!Sa.has(n))&&Fe(t,"has",n),s}ownKeys(t){return Fe(t,"iterate",J(t)?"length":ln),Reflect.ownKeys(t)}}class Gc extends wa{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const $c=new _a,Kc=new Gc,Wc=new _a(!0);const Mo=e=>e,fs=e=>Reflect.getPrototypeOf(e);function zc(e,t,n){return function(...s){const o=this.__v_raw,r=ue(o),i=bn(r),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,u=o[e](...s),c=n?Mo:t?Rs:Ne;return!t&&Fe(r,"iterate",l?Do:ln),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:a?[c(f[0]),c(f[1])]:c(f),done:p}},[Symbol.iterator](){return this}}}}function ds(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function qc(e,t){const n={get(o){const r=this.__v_raw,i=ue(r),a=ue(o);e||(qt(o,a)&&Fe(i,"get",o),Fe(i,"get",a));const{has:l}=fs(i),u=t?Mo:e?Rs:Ne;if(l.call(i,o))return u(r.get(o));if(l.call(i,a))return u(r.get(a));r!==i&&r.get(o)},get size(){const o=this.__v_raw;return!e&&Fe(ue(o),"iterate",ln),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,i=ue(r),a=ue(o);return e||(qt(o,a)&&Fe(i,"has",o),Fe(i,"has",a)),o===a?r.has(o):r.has(o)||r.has(a)},forEach(o,r){const i=this,a=i.__v_raw,l=ue(a),u=t?Mo:e?Rs:Ne;return!e&&Fe(l,"iterate",ln),a.forEach((c,f)=>o.call(r,u(c),u(f),i))}};return Ie(n,e?{add:ds("add"),set:ds("set"),delete:ds("delete"),clear:ds("clear")}:{add(o){!t&&!tt(o)&&!Xt(o)&&(o=ue(o));const r=ue(this);return fs(r).has.call(r,o)||(r.add(o),Tt(r,"add",o,o)),this},set(o,r){!t&&!tt(r)&&!Xt(r)&&(r=ue(r));const i=ue(this),{has:a,get:l}=fs(i);let u=a.call(i,o);u||(o=ue(o),u=a.call(i,o));const c=l.call(i,o);return i.set(o,r),u?qt(r,c)&&Tt(i,"set",o,r):Tt(i,"add",o,r),this},delete(o){const r=ue(this),{has:i,get:a}=fs(r);let l=i.call(r,o);l||(o=ue(o),l=i.call(r,o)),a&&a.call(r,o);const u=r.delete(o);return l&&Tt(r,"delete",o,void 0),u},clear(){const o=ue(this),r=o.size!==0,i=o.clear();return r&&Tt(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=zc(o,e,t)}),n}function ur(e,t){const n=qc(e,t);return(s,o,r)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(pe(n,o)&&o in s?n:s,o,r)}const Jc={get:ur(!1,!1)},Xc={get:ur(!1,!0)},Yc={get:ur(!0,!1)};const Ca=new WeakMap,xa=new WeakMap,Ea=new WeakMap,Qc=new WeakMap;function Zc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function eu(e){return e.__v_skip||!Object.isExtensible(e)?0:Zc(xc(e))}function rs(e){return Xt(e)?e:fr(e,!1,$c,Jc,Ca)}function Ra(e){return fr(e,!1,Wc,Xc,xa)}function Ta(e){return fr(e,!0,Kc,Yc,Ea)}function fr(e,t,n,s,o){if(!ve(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=eu(e);if(r===0)return e;const i=o.get(e);if(i)return i;const a=new Proxy(e,r===2?s:n);return o.set(e,a),a}function Jt(e){return Xt(e)?Jt(e.__v_raw):!!(e&&e.__v_isReactive)}function Xt(e){return!!(e&&e.__v_isReadonly)}function tt(e){return!!(e&&e.__v_isShallow)}function dr(e){return e?!!e.__v_raw:!1}function ue(e){const t=e&&e.__v_raw;return t?ue(t):e}function hr(e){return!pe(e,"__v_skip")&&Object.isExtensible(e)&&ko(e,"__v_skip",!0),e}const Ne=e=>ve(e)?rs(e):e,Rs=e=>ve(e)?Ta(e):e;function Pe(e){return e?e.__v_isRef===!0:!1}function oe(e){return Aa(e,!1)}function tu(e){return Aa(e,!0)}function Aa(e,t){return Pe(e)?e:new nu(e,t)}class nu{constructor(t,n){this.dep=new cr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ue(t),this._value=n?t:Ne(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||tt(t)||Xt(t);t=s?t:ue(t),qt(t,n)&&(this._rawValue=t,this._value=s?t:Ne(t),this.dep.trigger())}}function Sn(e){return Pe(e)?e.value:e}const su={get:(e,t,n)=>t==="__v_raw"?e:Sn(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return Pe(o)&&!Pe(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function Pa(e){return Jt(e)?e:new Proxy(e,su)}function ou(e){const t=J(e)?new Array(e.length):{};for(const n in e)t[n]=iu(e,n);return t}class ru{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Fc(ue(this._object),this._key)}}function iu(e,t,n){const s=e[t];return Pe(s)?s:new ru(e,t,n)}class au{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new cr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Jn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&Se!==this)return pa(this,!0),!0}get value(){const t=this.dep.track();return ya(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function lu(e,t,n=!1){let s,o;return te(e)?s=e:(s=e.get,o=e.set),new au(s,o,n)}const hs={},Ts=new WeakMap;let nn;function cu(e,t=!1,n=nn){if(n){let s=Ts.get(n);s||Ts.set(n,s=[]),s.push(e)}}function uu(e,t,n=ye){const{immediate:s,deep:o,once:r,scheduler:i,augmentJob:a,call:l}=n,u=P=>o?P:tt(P)||o===!1||o===0?At(P,1):At(P);let c,f,p,g,y=!1,S=!1;if(Pe(e)?(f=()=>e.value,y=tt(e)):Jt(e)?(f=()=>u(e),y=!0):J(e)?(S=!0,y=e.some(P=>Jt(P)||tt(P)),f=()=>e.map(P=>{if(Pe(P))return P.value;if(Jt(P))return u(P);if(te(P))return l?l(P,2):P()})):te(e)?t?f=l?()=>l(e,2):e:f=()=>{if(p){Ot();try{p()}finally{kt()}}const P=nn;nn=c;try{return l?l(e,3,[g]):e(g)}finally{nn=P}}:f=at,t&&o){const P=f,U=o===!0?1/0:o;f=()=>At(P(),U)}const w=fa(),k=()=>{c.stop(),w&&w.active&&or(w.effects,c)};if(r&&t){const P=t;t=(...U)=>{P(...U),k()}}let T=S?new Array(e.length).fill(hs):hs;const E=P=>{if(!(!(c.flags&1)||!c.dirty&&!P))if(t){const U=c.run();if(o||y||(S?U.some((Q,Y)=>qt(Q,T[Y])):qt(U,T))){p&&p();const Q=nn;nn=c;try{const Y=[U,T===hs?void 0:S&&T[0]===hs?[]:T,g];T=U,l?l(t,3,Y):t(...Y)}finally{nn=Q}}}else c.run()};return a&&a(E),c=new da(f),c.scheduler=i?()=>i(E,!1):E,g=P=>cu(P,!1,c),p=c.onStop=()=>{const P=Ts.get(c);if(P){if(l)l(P,4);else for(const U of P)U();Ts.delete(c)}},t?s?E(!0):T=c.run():i?i(E.bind(null,!0),!0):c.run(),k.pause=c.pause.bind(c),k.resume=c.resume.bind(c),k.stop=k,k}function At(e,t=1/0,n){if(t<=0||!ve(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Pe(e))At(e.value,t,n);else if(J(e))for(let s=0;s<e.length;s++)At(e[s],t,n);else if(Bs(e)||bn(e))e.forEach(s=>{At(s,t,n)});else if(oa(e)){for(const s in e)At(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&At(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function is(e,t,n,s){try{return s?e(...s):e()}catch(o){zs(o,t,n)}}function ut(e,t,n,s){if(te(e)){const o=is(e,t,n,s);return o&&na(o)&&o.catch(r=>{zs(r,t,n)}),o}if(J(e)){const o=[];for(let r=0;r<e.length;r++)o.push(ut(e[r],t,n,s));return o}}function zs(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ye;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,u)===!1)return}a=a.parent}if(r){Ot(),is(r,null,10,[e,l,u]),kt();return}}fu(e,n,o,s,i)}function fu(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const je=[];let yt=-1;const wn=[];let Bt=null,gn=0;const Ia=Promise.resolve();let As=null;function Pt(e){const t=As||Ia;return e?t.then(this?e.bind(this):e):t}function du(e){let t=yt+1,n=je.length;for(;t<n;){const s=t+n>>>1,o=je[s],r=Yn(o);r<e||r===e&&o.flags&2?t=s+1:n=s}return t}function pr(e){if(!(e.flags&1)){const t=Yn(e),n=je[je.length-1];!n||!(e.flags&2)&&t>=Yn(n)?je.push(e):je.splice(du(t),0,e),e.flags|=1,Oa()}}function Oa(){As||(As=Ia.then(La))}function hu(e){J(e)?wn.push(...e):Bt&&e.id===-1?Bt.splice(gn+1,0,e):e.flags&1||(wn.push(e),e.flags|=1),Oa()}function Ur(e,t,n=yt+1){for(;n<je.length;n++){const s=je[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;je.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ka(e){if(wn.length){const t=[...new Set(wn)].sort((n,s)=>Yn(n)-Yn(s));if(wn.length=0,Bt){Bt.push(...t);return}for(Bt=t,gn=0;gn<Bt.length;gn++){const n=Bt[gn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Bt=null,gn=0}}const Yn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function La(e){const t=at;try{for(yt=0;yt<je.length;yt++){const n=je[yt];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),is(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;yt<je.length;yt++){const n=je[yt];n&&(n.flags&=-2)}yt=-1,je.length=0,ka(),As=null,(je.length||wn.length)&&La()}}let Qe=null,Na=null;function Ps(e){const t=Qe;return Qe=e,Na=e&&e.type.__scopeId||null,t}function Vn(e,t=Qe,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&Jr(-1);const r=Ps(t);let i;try{i=e(...o)}finally{Ps(r),s._d&&Jr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Wt(e,t){if(Qe===null)return e;const n=eo(Qe),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[r,i,a,l=ye]=t[o];r&&(te(r)&&(r={mounted:r,updated:r}),r.deep&&At(i),s.push({dir:r,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function Zt(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const a=o[i];r&&(a.oldValue=r[i].value);let l=a.dir[s];l&&(Ot(),ut(l,n,8,[e.el,a,e,t]),kt())}}const pu=Symbol("_vte"),Da=e=>e.__isTeleport,jt=Symbol("_leaveCb"),ps=Symbol("_enterCb");function Ma(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return dn(()=>{e.isMounted=!0}),Ka(()=>{e.isUnmounting=!0}),e}const et=[Function,Array],Ha={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:et,onEnter:et,onAfterEnter:et,onEnterCancelled:et,onBeforeLeave:et,onLeave:et,onAfterLeave:et,onLeaveCancelled:et,onBeforeAppear:et,onAppear:et,onAfterAppear:et,onAppearCancelled:et},Fa=e=>{const t=e.subTree;return t.component?Fa(t.component):t},mu={name:"BaseTransition",props:Ha,setup(e,{slots:t}){const n=Zs(),s=Ma();return()=>{const o=t.default&&mr(t.default(),!0);if(!o||!o.length)return;const r=Ua(o),i=ue(e),{mode:a}=i;if(s.isLeaving)return yo(r);const l=Vr(r);if(!l)return yo(r);let u=Qn(l,i,s,n,f=>u=f);l.type!==Ge&&un(l,u);let c=n.subTree&&Vr(n.subTree);if(c&&c.type!==Ge&&!sn(l,c)&&Fa(n).type!==Ge){let f=Qn(c,i,s,n);if(un(c,f),a==="out-in"&&l.type!==Ge)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},yo(r);a==="in-out"&&l.type!==Ge?f.delayLeave=(p,g,y)=>{const S=Va(s,c);S[String(c.key)]=c,p[jt]=()=>{g(),p[jt]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{y(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return r}}};function Ua(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Ge){t=n;break}}return t}const gu=mu;function Va(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Qn(e,t,n,s,o){const{appear:r,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:p,onLeave:g,onAfterLeave:y,onLeaveCancelled:S,onBeforeAppear:w,onAppear:k,onAfterAppear:T,onAppearCancelled:E}=t,P=String(e.key),U=Va(n,e),Q=(x,z)=>{x&&ut(x,s,9,z)},Y=(x,z)=>{const ne=z[1];Q(x,z),J(x)?x.every(L=>L.length<=1)&&ne():x.length<=1&&ne()},q={mode:i,persisted:a,beforeEnter(x){let z=l;if(!n.isMounted)if(r)z=w||l;else return;x[jt]&&x[jt](!0);const ne=U[P];ne&&sn(e,ne)&&ne.el[jt]&&ne.el[jt](),Q(z,[x])},enter(x){let z=u,ne=c,L=f;if(!n.isMounted)if(r)z=k||u,ne=T||c,L=E||f;else return;let ie=!1;const we=x[ps]=Le=>{ie||(ie=!0,Le?Q(L,[x]):Q(ne,[x]),q.delayedLeave&&q.delayedLeave(),x[ps]=void 0)};z?Y(z,[x,we]):we()},leave(x,z){const ne=String(e.key);if(x[ps]&&x[ps](!0),n.isUnmounting)return z();Q(p,[x]);let L=!1;const ie=x[jt]=we=>{L||(L=!0,z(),we?Q(S,[x]):Q(y,[x]),x[jt]=void 0,U[ne]===e&&delete U[ne])};U[ne]=e,g?Y(g,[x,ie]):ie()},clone(x){const z=Qn(x,t,n,s,o);return o&&o(z),z}};return q}function yo(e){if(qs(e))return e=Yt(e),e.children=null,e}function Vr(e){if(!qs(e))return Da(e.type)&&e.children?Ua(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&te(n.default))return n.default()}}function un(e,t){e.shapeFlag&6&&e.component?(e.transition=t,un(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function mr(e,t=!1,n){let s=[],o=0;for(let r=0;r<e.length;r++){let i=e[r];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:r);i.type===Ce?(i.patchFlag&128&&o++,s=s.concat(mr(i.children,t,a))):(t||i.type!==Ge)&&s.push(a!=null?Yt(i,{key:a}):i)}if(o>1)for(let r=0;r<s.length;r++)s[r].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Ba(e,t){return te(e)?(()=>Ie({name:e.name},t,{setup:e}))():e}function ja(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Bn(e,t,n,s,o=!1){if(J(e)){e.forEach((y,S)=>Bn(y,t&&(J(t)?t[S]:t),n,s,o));return}if(jn(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Bn(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?eo(s.component):s.el,i=o?null:r,{i:a,r:l}=e,u=t&&t.r,c=a.refs===ye?a.refs={}:a.refs,f=a.setupState,p=ue(f),g=f===ye?()=>!1:y=>pe(p,y);if(u!=null&&u!==l&&(Oe(u)?(c[u]=null,g(u)&&(f[u]=null)):Pe(u)&&(u.value=null)),te(l))is(l,a,12,[i,c]);else{const y=Oe(l),S=Pe(l);if(y||S){const w=()=>{if(e.f){const k=y?g(l)?f[l]:c[l]:l.value;o?J(k)&&or(k,r):J(k)?k.includes(r)||k.push(r):y?(c[l]=[r],g(l)&&(f[l]=c[l])):(l.value=[r],e.k&&(c[e.k]=l.value))}else y?(c[l]=i,g(l)&&(f[l]=i)):S&&(l.value=i,e.k&&(c[e.k]=i))};i?(w.id=-1,Xe(w,n)):w()}}}$s().requestIdleCallback;$s().cancelIdleCallback;const jn=e=>!!e.type.__asyncLoader,qs=e=>e.type.__isKeepAlive;function yu(e,t){Ga(e,"a",t)}function vu(e,t){Ga(e,"da",t)}function Ga(e,t,n=Ue){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Js(t,s,n),n){let o=n.parent;for(;o&&o.parent;)qs(o.parent.vnode)&&bu(s,t,n,o),o=o.parent}}function bu(e,t,n,s){const o=Js(t,e,s,!0);Xs(()=>{or(s[t],o)},n)}function Js(e,t,n=Ue,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{Ot();const a=as(n),l=ut(t,n,e,i);return a(),kt(),l});return s?o.unshift(r):o.push(r),r}}const Lt=e=>(t,n=Ue)=>{(!es||e==="sp")&&Js(e,(...s)=>t(...s),n)},Su=Lt("bm"),dn=Lt("m"),wu=Lt("bu"),$a=Lt("u"),Ka=Lt("bum"),Xs=Lt("um"),_u=Lt("sp"),Cu=Lt("rtg"),xu=Lt("rtc");function Eu(e,t=Ue){Js("ec",e,t)}const Wa="components";function Is(e,t){return Tu(Wa,e,!0,t)||e}const Ru=Symbol.for("v-ndc");function Tu(e,t,n=!0,s=!1){const o=Qe||Ue;if(o){const r=o.type;if(e===Wa){const a=mf(r,!1);if(a&&(a===t||a===ot(t)||a===Gs(ot(t))))return r}const i=Br(o[e]||r[e],t)||Br(o.appContext[e],t);return!i&&s?r:i}}function Br(e,t){return e&&(e[t]||e[ot(t)]||e[Gs(ot(t))])}function nt(e,t,n,s){let o;const r=n&&n[s],i=J(e);if(i||Oe(e)){const a=i&&Jt(e);let l=!1,u=!1;a&&(l=!tt(e),u=Xt(e),e=Ws(e)),o=new Array(e.length);for(let c=0,f=e.length;c<f;c++)o[c]=t(l?u?Rs(Ne(e[c])):Ne(e[c]):e[c],c,void 0,r&&r[c])}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,r&&r[a])}else if(ve(e))if(e[Symbol.iterator])o=Array.from(e,(a,l)=>t(a,l,void 0,r&&r[l]));else{const a=Object.keys(e);o=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];o[l]=t(e[c],c,l,r&&r[l])}}else o=[];return n&&(n[s]=o),o}const Ho=e=>e?dl(e)?eo(e):Ho(e.parent):null,Gn=Ie(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ho(e.parent),$root:e=>Ho(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>gr(e),$forceUpdate:e=>e.f||(e.f=()=>{pr(e.update)}),$nextTick:e=>e.n||(e.n=Pt.bind(e.proxy)),$watch:e=>Ju.bind(e)}),vo=(e,t)=>e!==ye&&!e.__isScriptSetup&&pe(e,t),Au={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:r,accessCache:i,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return r[t]}else{if(vo(s,t))return i[t]=1,s[t];if(o!==ye&&pe(o,t))return i[t]=2,o[t];if((u=e.propsOptions[0])&&pe(u,t))return i[t]=3,r[t];if(n!==ye&&pe(n,t))return i[t]=4,n[t];Fo&&(i[t]=0)}}const c=Gn[t];let f,p;if(c)return t==="$attrs"&&Fe(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==ye&&pe(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,pe(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:r}=e;return vo(o,t)?(o[t]=n,!0):s!==ye&&pe(s,t)?(s[t]=n,!0):pe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:r}},i){let a;return!!n[i]||e!==ye&&pe(e,i)||vo(t,i)||(a=r[0])&&pe(a,i)||pe(s,i)||pe(Gn,i)||pe(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:pe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function jr(e){return J(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Fo=!0;function Pu(e){const t=gr(e),n=e.proxy,s=e.ctx;Fo=!1,t.beforeCreate&&Gr(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:i,watch:a,provide:l,inject:u,created:c,beforeMount:f,mounted:p,beforeUpdate:g,updated:y,activated:S,deactivated:w,beforeDestroy:k,beforeUnmount:T,destroyed:E,unmounted:P,render:U,renderTracked:Q,renderTriggered:Y,errorCaptured:q,serverPrefetch:x,expose:z,inheritAttrs:ne,components:L,directives:ie,filters:we}=t;if(u&&Iu(u,s,null),i)for(const Z in i){const ae=i[Z];te(ae)&&(s[Z]=ae.bind(n))}if(o){const Z=o.call(n,n);ve(Z)&&(e.data=rs(Z))}if(Fo=!0,r)for(const Z in r){const ae=r[Z],V=te(ae)?ae.bind(n,n):te(ae.get)?ae.get.bind(n,n):at,le=!te(ae)&&te(ae.set)?ae.set.bind(n):at,Ae=Ee({get:V,set:le});Object.defineProperty(s,Z,{enumerable:!0,configurable:!0,get:()=>Ae.value,set:I=>Ae.value=I})}if(a)for(const Z in a)za(a[Z],s,n,Z);if(l){const Z=te(l)?l.call(n):l;Reflect.ownKeys(Z).forEach(ae=>{gs(ae,Z[ae])})}c&&Gr(c,e,"c");function fe(Z,ae){J(ae)?ae.forEach(V=>Z(V.bind(n))):ae&&Z(ae.bind(n))}if(fe(Su,f),fe(dn,p),fe(wu,g),fe($a,y),fe(yu,S),fe(vu,w),fe(Eu,q),fe(xu,Q),fe(Cu,Y),fe(Ka,T),fe(Xs,P),fe(_u,x),J(z))if(z.length){const Z=e.exposed||(e.exposed={});z.forEach(ae=>{Object.defineProperty(Z,ae,{get:()=>n[ae],set:V=>n[ae]=V,enumerable:!0})})}else e.exposed||(e.exposed={});U&&e.render===at&&(e.render=U),ne!=null&&(e.inheritAttrs=ne),L&&(e.components=L),ie&&(e.directives=ie),x&&ja(e)}function Iu(e,t,n=at){J(e)&&(e=Uo(e));for(const s in e){const o=e[s];let r;ve(o)?"default"in o?r=st(o.from||s,o.default,!0):r=st(o.from||s):r=st(o),Pe(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[s]=r}}function Gr(e,t,n){ut(J(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function za(e,t,n,s){let o=s.includes(".")?il(n,s):()=>n[s];if(Oe(e)){const r=t[e];te(r)&&St(o,r)}else if(te(e))St(o,e.bind(n));else if(ve(e))if(J(e))e.forEach(r=>za(r,t,n,s));else{const r=te(e.handler)?e.handler.bind(n):t[e.handler];te(r)&&St(o,r,e)}}function gr(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,a=r.get(t);let l;return a?l=a:!o.length&&!n&&!s?l=t:(l={},o.length&&o.forEach(u=>Os(l,u,i,!0)),Os(l,t,i)),ve(t)&&r.set(t,l),l}function Os(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&Os(e,r,n,!0),o&&o.forEach(i=>Os(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const a=Ou[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Ou={data:$r,props:Kr,emits:Kr,methods:Nn,computed:Nn,beforeCreate:Be,created:Be,beforeMount:Be,mounted:Be,beforeUpdate:Be,updated:Be,beforeDestroy:Be,beforeUnmount:Be,destroyed:Be,unmounted:Be,activated:Be,deactivated:Be,errorCaptured:Be,serverPrefetch:Be,components:Nn,directives:Nn,watch:Lu,provide:$r,inject:ku};function $r(e,t){return t?e?function(){return Ie(te(e)?e.call(this,this):e,te(t)?t.call(this,this):t)}:t:e}function ku(e,t){return Nn(Uo(e),Uo(t))}function Uo(e){if(J(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Be(e,t){return e?[...new Set([].concat(e,t))]:t}function Nn(e,t){return e?Ie(Object.create(null),e,t):t}function Kr(e,t){return e?J(e)&&J(t)?[...new Set([...e,...t])]:Ie(Object.create(null),jr(e),jr(t??{})):t}function Lu(e,t){if(!e)return t;if(!t)return e;const n=Ie(Object.create(null),e);for(const s in t)n[s]=Be(e[s],t[s]);return n}function qa(){return{app:null,config:{isNativeTag:_c,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Nu=0;function Du(e,t){return function(s,o=null){te(s)||(s=Ie({},s)),o!=null&&!ve(o)&&(o=null);const r=qa(),i=new WeakSet,a=[];let l=!1;const u=r.app={_uid:Nu++,_component:s,_props:o,_container:null,_context:r,_instance:null,version:yf,get config(){return r.config},set config(c){},use(c,...f){return i.has(c)||(c&&te(c.install)?(i.add(c),c.install(u,...f)):te(c)&&(i.add(c),c(u,...f))),u},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),u},component(c,f){return f?(r.components[c]=f,u):r.components[c]},directive(c,f){return f?(r.directives[c]=f,u):r.directives[c]},mount(c,f,p){if(!l){const g=u._ceVNode||Te(s,o);return g.appContext=r,p===!0?p="svg":p===!1&&(p=void 0),f&&t?t(g,c):e(g,c,p),l=!0,u._container=c,c.__vue_app__=u,eo(g.component)}},onUnmount(c){a.push(c)},unmount(){l&&(ut(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return r.provides[c]=f,u},runWithContext(c){const f=cn;cn=u;try{return c()}finally{cn=f}}};return u}}let cn=null;function gs(e,t){if(Ue){let n=Ue.provides;const s=Ue.parent&&Ue.parent.provides;s===n&&(n=Ue.provides=Object.create(s)),n[e]=t}}function st(e,t,n=!1){const s=Zs();if(s||cn){let o=cn?cn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&te(t)?t.call(s&&s.proxy):t}}function Mu(){return!!(Zs()||cn)}const Ja={},Xa=()=>Object.create(Ja),Ya=e=>Object.getPrototypeOf(e)===Ja;function Hu(e,t,n,s=!1){const o={},r=Xa();e.propsDefaults=Object.create(null),Qa(e,t,o,r);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=s?o:Ra(o):e.type.props?e.props=o:e.props=r,e.attrs=r}function Fu(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,a=ue(o),[l]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let p=c[f];if(Ys(e.emitsOptions,p))continue;const g=t[p];if(l)if(pe(r,p))g!==r[p]&&(r[p]=g,u=!0);else{const y=ot(p);o[y]=Vo(l,a,y,g,e,!1)}else g!==r[p]&&(r[p]=g,u=!0)}}}else{Qa(e,t,o,r)&&(u=!0);let c;for(const f in a)(!t||!pe(t,f)&&((c=Qt(f))===f||!pe(t,c)))&&(l?n&&(n[f]!==void 0||n[c]!==void 0)&&(o[f]=Vo(l,a,f,void 0,e,!0)):delete o[f]);if(r!==a)for(const f in r)(!t||!pe(t,f))&&(delete r[f],u=!0)}u&&Tt(e.attrs,"set","")}function Qa(e,t,n,s){const[o,r]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(Hn(l))continue;const u=t[l];let c;o&&pe(o,c=ot(l))?!r||!r.includes(c)?n[c]=u:(a||(a={}))[c]=u:Ys(e.emitsOptions,l)||(!(l in s)||u!==s[l])&&(s[l]=u,i=!0)}if(r){const l=ue(n),u=a||ye;for(let c=0;c<r.length;c++){const f=r[c];n[f]=Vo(o,l,f,u[f],e,!pe(u,f))}}return i}function Vo(e,t,n,s,o,r){const i=e[n];if(i!=null){const a=pe(i,"default");if(a&&s===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&te(l)){const{propsDefaults:u}=o;if(n in u)s=u[n];else{const c=as(o);s=u[n]=l.call(null,t),c()}}else s=l;o.ce&&o.ce._setProp(n,s)}i[0]&&(r&&!a?s=!1:i[1]&&(s===""||s===Qt(n))&&(s=!0))}return s}const Uu=new WeakMap;function Za(e,t,n=!1){const s=n?Uu:t.propsCache,o=s.get(e);if(o)return o;const r=e.props,i={},a=[];let l=!1;if(!te(e)){const c=f=>{l=!0;const[p,g]=Za(f,t,!0);Ie(i,p),g&&a.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!l)return ve(e)&&s.set(e,vn),vn;if(J(r))for(let c=0;c<r.length;c++){const f=ot(r[c]);Wr(f)&&(i[f]=ye)}else if(r)for(const c in r){const f=ot(c);if(Wr(f)){const p=r[c],g=i[f]=J(p)||te(p)?{type:p}:Ie({},p),y=g.type;let S=!1,w=!0;if(J(y))for(let k=0;k<y.length;++k){const T=y[k],E=te(T)&&T.name;if(E==="Boolean"){S=!0;break}else E==="String"&&(w=!1)}else S=te(y)&&y.name==="Boolean";g[0]=S,g[1]=w,(S||pe(g,"default"))&&a.push(f)}}const u=[i,a];return ve(e)&&s.set(e,u),u}function Wr(e){return e[0]!=="$"&&!Hn(e)}const yr=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",vr=e=>J(e)?e.map(bt):[bt(e)],Vu=(e,t,n)=>{if(t._n)return t;const s=Vn((...o)=>vr(t(...o)),n);return s._c=!1,s},el=(e,t,n)=>{const s=e._ctx;for(const o in e){if(yr(o))continue;const r=e[o];if(te(r))t[o]=Vu(o,r,s);else if(r!=null){const i=vr(r);t[o]=()=>i}}},tl=(e,t)=>{const n=vr(t);e.slots.default=()=>n},nl=(e,t,n)=>{for(const s in t)(n||!yr(s))&&(e[s]=t[s])},Bu=(e,t,n)=>{const s=e.slots=Xa();if(e.vnode.shapeFlag&32){const o=t.__;o&&ko(s,"__",o,!0);const r=t._;r?(nl(s,t,n),n&&ko(s,"_",r,!0)):el(t,s)}else t&&tl(e,t)},ju=(e,t,n)=>{const{vnode:s,slots:o}=e;let r=!0,i=ye;if(s.shapeFlag&32){const a=t._;a?n&&a===1?r=!1:nl(o,t,n):(r=!t.$stable,el(t,o)),i=t}else t&&(tl(e,t),i={default:1});if(r)for(const a in o)!yr(a)&&i[a]==null&&delete o[a]},Xe=nf;function Gu(e){return $u(e)}function $u(e,t){const n=$s();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:i,createText:a,createComment:l,setText:u,setElementText:c,parentNode:f,nextSibling:p,setScopeId:g=at,insertStaticContent:y}=e,S=(h,m,v,R=null,_=null,A=null,M=void 0,D=null,N=!!m.dynamicChildren)=>{if(h===m)return;h&&!sn(h,m)&&(R=C(h),I(h,_,A,!0),h=null),m.patchFlag===-2&&(N=!1,m.dynamicChildren=null);const{type:O,ref:X,shapeFlag:F}=m;switch(O){case Qs:w(h,m,v,R);break;case Ge:k(h,m,v,R);break;case ys:h==null&&T(m,v,R,M);break;case Ce:L(h,m,v,R,_,A,M,D,N);break;default:F&1?U(h,m,v,R,_,A,M,D,N):F&6?ie(h,m,v,R,_,A,M,D,N):(F&64||F&128)&&O.process(h,m,v,R,_,A,M,D,N,K)}X!=null&&_?Bn(X,h&&h.ref,A,m||h,!m):X==null&&h&&h.ref!=null&&Bn(h.ref,null,A,h,!0)},w=(h,m,v,R)=>{if(h==null)s(m.el=a(m.children),v,R);else{const _=m.el=h.el;m.children!==h.children&&u(_,m.children)}},k=(h,m,v,R)=>{h==null?s(m.el=l(m.children||""),v,R):m.el=h.el},T=(h,m,v,R)=>{[h.el,h.anchor]=y(h.children,m,v,R,h.el,h.anchor)},E=({el:h,anchor:m},v,R)=>{let _;for(;h&&h!==m;)_=p(h),s(h,v,R),h=_;s(m,v,R)},P=({el:h,anchor:m})=>{let v;for(;h&&h!==m;)v=p(h),o(h),h=v;o(m)},U=(h,m,v,R,_,A,M,D,N)=>{m.type==="svg"?M="svg":m.type==="math"&&(M="mathml"),h==null?Q(m,v,R,_,A,M,D,N):x(h,m,_,A,M,D,N)},Q=(h,m,v,R,_,A,M,D)=>{let N,O;const{props:X,shapeFlag:F,transition:W,dirs:ee}=h;if(N=h.el=i(h.type,A,X&&X.is,X),F&8?c(N,h.children):F&16&&q(h.children,N,null,R,_,bo(h,A),M,D),ee&&Zt(h,null,R,"created"),Y(N,h,h.scopeId,M,R),X){for(const be in X)be!=="value"&&!Hn(be)&&r(N,be,null,X[be],A,R);"value"in X&&r(N,"value",null,X.value,A),(O=X.onVnodeBeforeMount)&&pt(O,R,h)}ee&&Zt(h,null,R,"beforeMount");const ce=Ku(_,W);ce&&W.beforeEnter(N),s(N,m,v),((O=X&&X.onVnodeMounted)||ce||ee)&&Xe(()=>{O&&pt(O,R,h),ce&&W.enter(N),ee&&Zt(h,null,R,"mounted")},_)},Y=(h,m,v,R,_)=>{if(v&&g(h,v),R)for(let A=0;A<R.length;A++)g(h,R[A]);if(_){let A=_.subTree;if(m===A||ll(A.type)&&(A.ssContent===m||A.ssFallback===m)){const M=_.vnode;Y(h,M,M.scopeId,M.slotScopeIds,_.parent)}}},q=(h,m,v,R,_,A,M,D,N=0)=>{for(let O=N;O<h.length;O++){const X=h[O]=D?Gt(h[O]):bt(h[O]);S(null,X,m,v,R,_,A,M,D)}},x=(h,m,v,R,_,A,M)=>{const D=m.el=h.el;let{patchFlag:N,dynamicChildren:O,dirs:X}=m;N|=h.patchFlag&16;const F=h.props||ye,W=m.props||ye;let ee;if(v&&en(v,!1),(ee=W.onVnodeBeforeUpdate)&&pt(ee,v,m,h),X&&Zt(m,h,v,"beforeUpdate"),v&&en(v,!0),(F.innerHTML&&W.innerHTML==null||F.textContent&&W.textContent==null)&&c(D,""),O?z(h.dynamicChildren,O,D,v,R,bo(m,_),A):M||ae(h,m,D,null,v,R,bo(m,_),A,!1),N>0){if(N&16)ne(D,F,W,v,_);else if(N&2&&F.class!==W.class&&r(D,"class",null,W.class,_),N&4&&r(D,"style",F.style,W.style,_),N&8){const ce=m.dynamicProps;for(let be=0;be<ce.length;be++){const ge=ce[be],$e=F[ge],Me=W[ge];(Me!==$e||ge==="value")&&r(D,ge,$e,Me,_,v)}}N&1&&h.children!==m.children&&c(D,m.children)}else!M&&O==null&&ne(D,F,W,v,_);((ee=W.onVnodeUpdated)||X)&&Xe(()=>{ee&&pt(ee,v,m,h),X&&Zt(m,h,v,"updated")},R)},z=(h,m,v,R,_,A,M)=>{for(let D=0;D<m.length;D++){const N=h[D],O=m[D],X=N.el&&(N.type===Ce||!sn(N,O)||N.shapeFlag&198)?f(N.el):v;S(N,O,X,null,R,_,A,M,!0)}},ne=(h,m,v,R,_)=>{if(m!==v){if(m!==ye)for(const A in m)!Hn(A)&&!(A in v)&&r(h,A,m[A],null,_,R);for(const A in v){if(Hn(A))continue;const M=v[A],D=m[A];M!==D&&A!=="value"&&r(h,A,D,M,_,R)}"value"in v&&r(h,"value",m.value,v.value,_)}},L=(h,m,v,R,_,A,M,D,N)=>{const O=m.el=h?h.el:a(""),X=m.anchor=h?h.anchor:a("");let{patchFlag:F,dynamicChildren:W,slotScopeIds:ee}=m;ee&&(D=D?D.concat(ee):ee),h==null?(s(O,v,R),s(X,v,R),q(m.children||[],v,X,_,A,M,D,N)):F>0&&F&64&&W&&h.dynamicChildren?(z(h.dynamicChildren,W,v,_,A,M,D),(m.key!=null||_&&m===_.subTree)&&sl(h,m,!0)):ae(h,m,v,X,_,A,M,D,N)},ie=(h,m,v,R,_,A,M,D,N)=>{m.slotScopeIds=D,h==null?m.shapeFlag&512?_.ctx.activate(m,v,R,M,N):we(m,v,R,_,A,M,N):Le(h,m,N)},we=(h,m,v,R,_,A,M)=>{const D=h.component=uf(h,R,_);if(qs(h)&&(D.ctx.renderer=K),ff(D,!1,M),D.asyncDep){if(_&&_.registerDep(D,fe,M),!h.el){const N=D.subTree=Te(Ge);k(null,N,m,v),h.placeholder=N.el}}else fe(D,h,m,v,_,A,M)},Le=(h,m,v)=>{const R=m.component=h.component;if(ef(h,m,v))if(R.asyncDep&&!R.asyncResolved){Z(R,m,v);return}else R.next=m,R.update();else m.el=h.el,R.vnode=m},fe=(h,m,v,R,_,A,M)=>{const D=()=>{if(h.isMounted){let{next:F,bu:W,u:ee,parent:ce,vnode:be}=h;{const qe=ol(h);if(qe){F&&(F.el=be.el,Z(h,F,M)),qe.asyncDep.then(()=>{h.isUnmounted||D()});return}}let ge=F,$e;en(h,!1),F?(F.el=be.el,Z(h,F,M)):F=be,W&&ms(W),($e=F.props&&F.props.onVnodeBeforeUpdate)&&pt($e,ce,F,be),en(h,!0);const Me=So(h),rt=h.subTree;h.subTree=Me,S(rt,Me,f(rt.el),C(rt),h,_,A),F.el=Me.el,ge===null&&tf(h,Me.el),ee&&Xe(ee,_),($e=F.props&&F.props.onVnodeUpdated)&&Xe(()=>pt($e,ce,F,be),_)}else{let F;const{el:W,props:ee}=m,{bm:ce,m:be,parent:ge,root:$e,type:Me}=h,rt=jn(m);if(en(h,!1),ce&&ms(ce),!rt&&(F=ee&&ee.onVnodeBeforeMount)&&pt(F,ge,m),en(h,!0),W&&_e){const qe=()=>{h.subTree=So(h),_e(W,h.subTree,h,_,null)};rt&&Me.__asyncHydrate?Me.__asyncHydrate(W,h,qe):qe()}else{$e.ce&&$e.ce._def.shadowRoot!==!1&&$e.ce._injectChildStyle(Me);const qe=h.subTree=So(h);S(null,qe,v,R,h,_,A),m.el=qe.el}if(be&&Xe(be,_),!rt&&(F=ee&&ee.onVnodeMounted)){const qe=m;Xe(()=>pt(F,ge,qe),_)}(m.shapeFlag&256||ge&&jn(ge.vnode)&&ge.vnode.shapeFlag&256)&&h.a&&Xe(h.a,_),h.isMounted=!0,m=v=R=null}};h.scope.on();const N=h.effect=new da(D);h.scope.off();const O=h.update=N.run.bind(N),X=h.job=N.runIfDirty.bind(N);X.i=h,X.id=h.uid,N.scheduler=()=>pr(X),en(h,!0),O()},Z=(h,m,v)=>{m.component=h;const R=h.vnode.props;h.vnode=m,h.next=null,Fu(h,m.props,R,v),ju(h,m.children,v),Ot(),Ur(h),kt()},ae=(h,m,v,R,_,A,M,D,N=!1)=>{const O=h&&h.children,X=h?h.shapeFlag:0,F=m.children,{patchFlag:W,shapeFlag:ee}=m;if(W>0){if(W&128){le(O,F,v,R,_,A,M,D,N);return}else if(W&256){V(O,F,v,R,_,A,M,D,N);return}}ee&8?(X&16&&De(O,_,A),F!==O&&c(v,F)):X&16?ee&16?le(O,F,v,R,_,A,M,D,N):De(O,_,A,!0):(X&8&&c(v,""),ee&16&&q(F,v,R,_,A,M,D,N))},V=(h,m,v,R,_,A,M,D,N)=>{h=h||vn,m=m||vn;const O=h.length,X=m.length,F=Math.min(O,X);let W;for(W=0;W<F;W++){const ee=m[W]=N?Gt(m[W]):bt(m[W]);S(h[W],ee,v,null,_,A,M,D,N)}O>X?De(h,_,A,!0,!1,F):q(m,v,R,_,A,M,D,N,F)},le=(h,m,v,R,_,A,M,D,N)=>{let O=0;const X=m.length;let F=h.length-1,W=X-1;for(;O<=F&&O<=W;){const ee=h[O],ce=m[O]=N?Gt(m[O]):bt(m[O]);if(sn(ee,ce))S(ee,ce,v,null,_,A,M,D,N);else break;O++}for(;O<=F&&O<=W;){const ee=h[F],ce=m[W]=N?Gt(m[W]):bt(m[W]);if(sn(ee,ce))S(ee,ce,v,null,_,A,M,D,N);else break;F--,W--}if(O>F){if(O<=W){const ee=W+1,ce=ee<X?m[ee].el:R;for(;O<=W;)S(null,m[O]=N?Gt(m[O]):bt(m[O]),v,ce,_,A,M,D,N),O++}}else if(O>W)for(;O<=F;)I(h[O],_,A,!0),O++;else{const ee=O,ce=O,be=new Map;for(O=ce;O<=W;O++){const Je=m[O]=N?Gt(m[O]):bt(m[O]);Je.key!=null&&be.set(Je.key,O)}let ge,$e=0;const Me=W-ce+1;let rt=!1,qe=0;const Pn=new Array(Me);for(O=0;O<Me;O++)Pn[O]=0;for(O=ee;O<=F;O++){const Je=h[O];if($e>=Me){I(Je,_,A,!0);continue}let ht;if(Je.key!=null)ht=be.get(Je.key);else for(ge=ce;ge<=W;ge++)if(Pn[ge-ce]===0&&sn(Je,m[ge])){ht=ge;break}ht===void 0?I(Je,_,A,!0):(Pn[ht-ce]=O+1,ht>=qe?qe=ht:rt=!0,S(Je,m[ht],v,null,_,A,M,D,N),$e++)}const kr=rt?Wu(Pn):vn;for(ge=kr.length-1,O=Me-1;O>=0;O--){const Je=ce+O,ht=m[Je],Lr=m[Je+1],Nr=Je+1<X?Lr.el||Lr.placeholder:R;Pn[O]===0?S(null,ht,v,Nr,_,A,M,D,N):rt&&(ge<0||O!==kr[ge]?Ae(ht,v,Nr,2):ge--)}}},Ae=(h,m,v,R,_=null)=>{const{el:A,type:M,transition:D,children:N,shapeFlag:O}=h;if(O&6){Ae(h.component.subTree,m,v,R);return}if(O&128){h.suspense.move(m,v,R);return}if(O&64){M.move(h,m,v,K);return}if(M===Ce){s(A,m,v);for(let F=0;F<N.length;F++)Ae(N[F],m,v,R);s(h.anchor,m,v);return}if(M===ys){E(h,m,v);return}if(R!==2&&O&1&&D)if(R===0)D.beforeEnter(A),s(A,m,v),Xe(()=>D.enter(A),_);else{const{leave:F,delayLeave:W,afterLeave:ee}=D,ce=()=>{h.ctx.isUnmounted?o(A):s(A,m,v)},be=()=>{F(A,()=>{ce(),ee&&ee()})};W?W(A,ce,be):be()}else s(A,m,v)},I=(h,m,v,R=!1,_=!1)=>{const{type:A,props:M,ref:D,children:N,dynamicChildren:O,shapeFlag:X,patchFlag:F,dirs:W,cacheIndex:ee}=h;if(F===-2&&(_=!1),D!=null&&(Ot(),Bn(D,null,v,h,!0),kt()),ee!=null&&(m.renderCache[ee]=void 0),X&256){m.ctx.deactivate(h);return}const ce=X&1&&W,be=!jn(h);let ge;if(be&&(ge=M&&M.onVnodeBeforeUnmount)&&pt(ge,m,h),X&6)Dt(h.component,v,R);else{if(X&128){h.suspense.unmount(v,R);return}ce&&Zt(h,null,m,"beforeUnmount"),X&64?h.type.remove(h,m,v,K,R):O&&!O.hasOnce&&(A!==Ce||F>0&&F&64)?De(O,m,v,!1,!0):(A===Ce&&F&384||!_&&X&16)&&De(N,m,v),R&&$(h)}(be&&(ge=M&&M.onVnodeUnmounted)||ce)&&Xe(()=>{ge&&pt(ge,m,h),ce&&Zt(h,null,m,"unmounted")},v)},$=h=>{const{type:m,el:v,anchor:R,transition:_}=h;if(m===Ce){me(v,R);return}if(m===ys){P(h);return}const A=()=>{o(v),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(h.shapeFlag&1&&_&&!_.persisted){const{leave:M,delayLeave:D}=_,N=()=>M(v,A);D?D(h.el,A,N):N()}else A()},me=(h,m)=>{let v;for(;h!==m;)v=p(h),o(h),h=v;o(m)},Dt=(h,m,v)=>{const{bum:R,scope:_,job:A,subTree:M,um:D,m:N,a:O,parent:X,slots:{__:F}}=h;zr(N),zr(O),R&&ms(R),X&&J(F)&&F.forEach(W=>{X.renderCache[W]=void 0}),_.stop(),A&&(A.flags|=8,I(M,h,m,v)),D&&Xe(D,m),Xe(()=>{h.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},De=(h,m,v,R=!1,_=!1,A=0)=>{for(let M=A;M<h.length;M++)I(h[M],m,v,R,_)},C=h=>{if(h.shapeFlag&6)return C(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const m=p(h.anchor||h.el),v=m&&m[pu];return v?p(v):m};let B=!1;const H=(h,m,v)=>{h==null?m._vnode&&I(m._vnode,null,null,!0):S(m._vnode||null,h,m,null,null,null,v),m._vnode=h,B||(B=!0,Ur(),ka(),B=!1)},K={p:S,um:I,m:Ae,r:$,mt:we,mc:q,pc:ae,pbc:z,n:C,o:e};let de,_e;return t&&([de,_e]=t(K)),{render:H,hydrate:de,createApp:Du(H,de)}}function bo({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function en({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ku(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function sl(e,t,n=!1){const s=e.children,o=t.children;if(J(s)&&J(o))for(let r=0;r<s.length;r++){const i=s[r];let a=o[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[r]=Gt(o[r]),a.el=i.el),!n&&a.patchFlag!==-2&&sl(i,a)),a.type===Qs&&(a.el=i.el),a.type===Ge&&!a.el&&(a.el=i.el)}}function Wu(e){const t=e.slice(),n=[0];let s,o,r,i,a;const l=e.length;for(s=0;s<l;s++){const u=e[s];if(u!==0){if(o=n[n.length-1],e[o]<u){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)a=r+i>>1,e[n[a]]<u?r=a+1:i=a;u<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function ol(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ol(t)}function zr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const zu=Symbol.for("v-scx"),qu=()=>st(zu);function St(e,t,n){return rl(e,t,n)}function rl(e,t,n=ye){const{immediate:s,deep:o,flush:r,once:i}=n,a=Ie({},n),l=t&&s||!t&&r!=="post";let u;if(es){if(r==="sync"){const g=qu();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!l){const g=()=>{};return g.stop=at,g.resume=at,g.pause=at,g}}const c=Ue;a.call=(g,y,S)=>ut(g,c,y,S);let f=!1;r==="post"?a.scheduler=g=>{Xe(g,c&&c.suspense)}:r!=="sync"&&(f=!0,a.scheduler=(g,y)=>{y?g():pr(g)}),a.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const p=uu(e,t,a);return es&&(u?u.push(p):l&&p()),p}function Ju(e,t,n){const s=this.proxy,o=Oe(e)?e.includes(".")?il(s,e):()=>s[e]:e.bind(s,s);let r;te(t)?r=t:(r=t.handler,n=t);const i=as(this),a=rl(o,r.bind(s),n);return i(),a}function il(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const Xu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ot(t)}Modifiers`]||e[`${Qt(t)}Modifiers`];function Yu(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ye;let o=n;const r=t.startsWith("update:"),i=r&&Xu(s,t.slice(7));i&&(i.trim&&(o=n.map(c=>Oe(c)?c.trim():c)),i.number&&(o=n.map(Lo)));let a,l=s[a=fo(t)]||s[a=fo(ot(t))];!l&&r&&(l=s[a=fo(Qt(t))]),l&&ut(l,e,6,o);const u=s[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,ut(u,e,6,o)}}function al(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const r=e.emits;let i={},a=!1;if(!te(e)){const l=u=>{const c=al(u,t,!0);c&&(a=!0,Ie(i,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!r&&!a?(ve(e)&&s.set(e,null),null):(J(r)?r.forEach(l=>i[l]=null):Ie(i,r),ve(e)&&s.set(e,i),i)}function Ys(e,t){return!e||!Vs(t)?!1:(t=t.slice(2).replace(/Once$/,""),pe(e,t[0].toLowerCase()+t.slice(1))||pe(e,Qt(t))||pe(e,t))}function So(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:i,attrs:a,emit:l,render:u,renderCache:c,props:f,data:p,setupState:g,ctx:y,inheritAttrs:S}=e,w=Ps(e);let k,T;try{if(n.shapeFlag&4){const P=o||s,U=P;k=bt(u.call(U,P,c,f,g,p,y)),T=a}else{const P=t;k=bt(P.length>1?P(f,{attrs:a,slots:i,emit:l}):P(f,null)),T=t.props?a:Qu(a)}}catch(P){$n.length=0,zs(P,e,1),k=Te(Ge)}let E=k;if(T&&S!==!1){const P=Object.keys(T),{shapeFlag:U}=E;P.length&&U&7&&(r&&P.some(sr)&&(T=Zu(T,r)),E=Yt(E,T,!1,!0))}return n.dirs&&(E=Yt(E,null,!1,!0),E.dirs=E.dirs?E.dirs.concat(n.dirs):n.dirs),n.transition&&un(E,n.transition),k=E,Ps(w),k}const Qu=e=>{let t;for(const n in e)(n==="class"||n==="style"||Vs(n))&&((t||(t={}))[n]=e[n]);return t},Zu=(e,t)=>{const n={};for(const s in e)(!sr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function ef(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:a,patchFlag:l}=t,u=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?qr(s,i,u):!!i;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const p=c[f];if(i[p]!==s[p]&&!Ys(u,p))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:s===i?!1:s?i?qr(s,i,u):!0:!!i;return!1}function qr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!Ys(n,r))return!0}return!1}function tf({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const ll=e=>e.__isSuspense;function nf(e,t){t&&t.pendingBranch?J(e)?t.effects.push(...e):t.effects.push(e):hu(e)}const Ce=Symbol.for("v-fgt"),Qs=Symbol.for("v-txt"),Ge=Symbol.for("v-cmt"),ys=Symbol.for("v-stc"),$n=[];let Ze=null;function j(e=!1){$n.push(Ze=e?null:[])}function sf(){$n.pop(),Ze=$n[$n.length-1]||null}let Zn=1;function Jr(e,t=!1){Zn+=e,e<0&&Ze&&t&&(Ze.hasOnce=!0)}function cl(e){return e.dynamicChildren=Zn>0?Ze||vn:null,sf(),Zn>0&&Ze&&Ze.push(e),e}function G(e,t,n,s,o,r){return cl(d(e,t,n,s,o,r,!0))}function ul(e,t,n,s,o){return cl(Te(e,t,n,s,o,!0))}function ks(e){return e?e.__v_isVNode===!0:!1}function sn(e,t){return e.type===t.type&&e.key===t.key}const fl=({key:e})=>e??null,vs=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Oe(e)||Pe(e)||te(e)?{i:Qe,r:e,k:t,f:!!n}:e:null);function d(e,t=null,n=null,s=0,o=null,r=e===Ce?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fl(t),ref:t&&vs(t),scopeId:Na,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Qe};return a?(br(l,n),r&128&&e.normalize(l)):n&&(l.shapeFlag|=Oe(n)?8:16),Zn>0&&!i&&Ze&&(l.patchFlag>0||r&6)&&l.patchFlag!==32&&Ze.push(l),l}const Te=of;function of(e,t=null,n=null,s=0,o=null,r=!1){if((!e||e===Ru)&&(e=Ge),ks(e)){const a=Yt(e,t,!0);return n&&br(a,n),Zn>0&&!r&&Ze&&(a.shapeFlag&6?Ze[Ze.indexOf(e)]=a:Ze.push(a)),a.patchFlag=-2,a}if(gf(e)&&(e=e.__vccOpts),t){t=rf(t);let{class:a,style:l}=t;a&&!Oe(a)&&(t.class=We(a)),ve(l)&&(dr(l)&&!J(l)&&(l=Ie({},l)),t.style=_t(l))}const i=Oe(e)?1:ll(e)?128:Da(e)?64:ve(e)?4:te(e)?2:0;return d(e,t,n,s,o,i,r,!0)}function rf(e){return e?dr(e)||Ya(e)?Ie({},e):e:null}function Yt(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:i,children:a,transition:l}=e,u=t?af(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&fl(u),ref:t&&t.ref?n&&r?J(r)?r.concat(vs(t)):[r,vs(t)]:vs(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ce?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Yt(e.ssContent),ssFallback:e.ssFallback&&Yt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&un(c,l.clone(c)),c}function It(e=" ",t=0){return Te(Qs,null,e,t)}function Dn(e,t){const n=Te(ys,null,e);return n.staticCount=t,n}function xe(e="",t=!1){return t?(j(),ul(Ge,null,e)):Te(Ge,null,e)}function bt(e){return e==null||typeof e=="boolean"?Te(Ge):J(e)?Te(Ce,null,e.slice()):ks(e)?Gt(e):Te(Qs,null,String(e))}function Gt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Yt(e)}function br(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(J(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),br(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Ya(t)?t._ctx=Qe:o===3&&Qe&&(Qe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else te(t)?(t={default:t,_ctx:Qe},n=32):(t=String(t),s&64?(n=16,t=[It(t)]):n=8);e.children=t,e.shapeFlag|=n}function af(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=We([t.class,s.class]));else if(o==="style")t.style=_t([t.style,s.style]);else if(Vs(o)){const r=t[o],i=s[o];i&&r!==i&&!(J(r)&&r.includes(i))&&(t[o]=r?[].concat(r,i):i)}else o!==""&&(t[o]=s[o])}return t}function pt(e,t,n,s=null){ut(e,t,7,[n,s])}const lf=qa();let cf=0;function uf(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||lf,r={uid:cf++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ca(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Za(s,o),emitsOptions:al(s,o),emit:null,emitted:null,propsDefaults:ye,inheritAttrs:s.inheritAttrs,ctx:ye,data:ye,props:ye,attrs:ye,slots:ye,refs:ye,setupState:ye,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Yu.bind(null,r),e.ce&&e.ce(r),r}let Ue=null;const Zs=()=>Ue||Qe;let Ls,Bo;{const e=$s(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),r=>{o.length>1?o.forEach(i=>i(r)):o[0](r)}};Ls=t("__VUE_INSTANCE_SETTERS__",n=>Ue=n),Bo=t("__VUE_SSR_SETTERS__",n=>es=n)}const as=e=>{const t=Ue;return Ls(e),e.scope.on(),()=>{e.scope.off(),Ls(t)}},Xr=()=>{Ue&&Ue.scope.off(),Ls(null)};function dl(e){return e.vnode.shapeFlag&4}let es=!1;function ff(e,t=!1,n=!1){t&&Bo(t);const{props:s,children:o}=e.vnode,r=dl(e);Hu(e,s,r,t),Bu(e,o,n||t);const i=r?df(e,t):void 0;return t&&Bo(!1),i}function df(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Au);const{setup:s}=n;if(s){Ot();const o=e.setupContext=s.length>1?pf(e):null,r=as(e),i=is(s,e,0,[e.props,o]),a=na(i);if(kt(),r(),(a||e.sp)&&!jn(e)&&ja(e),a){if(i.then(Xr,Xr),t)return i.then(l=>{Yr(e,l,t)}).catch(l=>{zs(l,e,0)});e.asyncDep=i}else Yr(e,i,t)}else hl(e,t)}function Yr(e,t,n){te(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ve(t)&&(e.setupState=Pa(t)),hl(e,n)}let Qr;function hl(e,t,n){const s=e.type;if(!e.render){if(!t&&Qr&&!s.render){const o=s.template||gr(e).template;if(o){const{isCustomElement:r,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:l}=s,u=Ie(Ie({isCustomElement:r,delimiters:a},i),l);s.render=Qr(o,u)}}e.render=s.render||at}{const o=as(e);Ot();try{Pu(e)}finally{kt(),o()}}}const hf={get(e,t){return Fe(e,"get",""),e[t]}};function pf(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,hf),slots:e.slots,emit:e.emit,expose:t}}function eo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Pa(hr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Gn)return Gn[n](e)},has(t,n){return n in t||n in Gn}})):e.proxy}function mf(e,t=!0){return te(e)?e.displayName||e.name:e.name||t&&e.__name}function gf(e){return te(e)&&"__vccOpts"in e}const Ee=(e,t)=>lu(e,t,es);function Sr(e,t,n){const s=arguments.length;return s===2?ve(t)&&!J(t)?ks(t)?Te(e,null,[t]):Te(e,t):Te(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&ks(n)&&(n=[n]),Te(e,t,n))}const yf="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let jo;const Zr=typeof window<"u"&&window.trustedTypes;if(Zr)try{jo=Zr.createPolicy("vue",{createHTML:e=>e})}catch{}const pl=jo?e=>jo.createHTML(e):e=>e,vf="http://www.w3.org/2000/svg",bf="http://www.w3.org/1998/Math/MathML",Rt=typeof document<"u"?document:null,ei=Rt&&Rt.createElement("template"),Sf={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?Rt.createElementNS(vf,e):t==="mathml"?Rt.createElementNS(bf,e):n?Rt.createElement(e,{is:n}):Rt.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>Rt.createTextNode(e),createComment:e=>Rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{ei.innerHTML=pl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=ei.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Mt="transition",On="animation",Cn=Symbol("_vtc"),ml={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},gl=Ie({},Ha,ml),wf=e=>(e.displayName="Transition",e.props=gl,e),Go=wf((e,{slots:t})=>Sr(gu,yl(e),t)),tn=(e,t=[])=>{J(e)?e.forEach(n=>n(...t)):e&&e(...t)},ti=e=>e?J(e)?e.some(t=>t.length>1):e.length>1:!1;function yl(e){const t={};for(const L in e)L in ml||(t[L]=e[L]);if(e.css===!1)return t;const{name:n="v",type:s,duration:o,enterFromClass:r=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=r,appearActiveClass:u=i,appearToClass:c=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,y=_f(o),S=y&&y[0],w=y&&y[1],{onBeforeEnter:k,onEnter:T,onEnterCancelled:E,onLeave:P,onLeaveCancelled:U,onBeforeAppear:Q=k,onAppear:Y=T,onAppearCancelled:q=E}=t,x=(L,ie,we,Le)=>{L._enterCancelled=Le,Ut(L,ie?c:a),Ut(L,ie?u:i),we&&we()},z=(L,ie)=>{L._isLeaving=!1,Ut(L,f),Ut(L,g),Ut(L,p),ie&&ie()},ne=L=>(ie,we)=>{const Le=L?Y:T,fe=()=>x(ie,L,we);tn(Le,[ie,fe]),ni(()=>{Ut(ie,L?l:r),gt(ie,L?c:a),ti(Le)||si(ie,s,S,fe)})};return Ie(t,{onBeforeEnter(L){tn(k,[L]),gt(L,r),gt(L,i)},onBeforeAppear(L){tn(Q,[L]),gt(L,l),gt(L,u)},onEnter:ne(!1),onAppear:ne(!0),onLeave(L,ie){L._isLeaving=!0;const we=()=>z(L,ie);gt(L,f),L._enterCancelled?(gt(L,p),$o()):($o(),gt(L,p)),ni(()=>{L._isLeaving&&(Ut(L,f),gt(L,g),ti(P)||si(L,s,w,we))}),tn(P,[L,we])},onEnterCancelled(L){x(L,!1,void 0,!0),tn(E,[L])},onAppearCancelled(L){x(L,!0,void 0,!0),tn(q,[L])},onLeaveCancelled(L){z(L),tn(U,[L])}})}function _f(e){if(e==null)return null;if(ve(e))return[wo(e.enter),wo(e.leave)];{const t=wo(e);return[t,t]}}function wo(e){return Tc(e)}function gt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Cn]||(e[Cn]=new Set)).add(t)}function Ut(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Cn];n&&(n.delete(t),n.size||(e[Cn]=void 0))}function ni(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Cf=0;function si(e,t,n,s){const o=e._endId=++Cf,r=()=>{o===e._endId&&s()};if(n!=null)return setTimeout(r,n);const{type:i,timeout:a,propCount:l}=vl(e,t);if(!i)return s();const u=i+"end";let c=0;const f=()=>{e.removeEventListener(u,p),r()},p=g=>{g.target===e&&++c>=l&&f()};setTimeout(()=>{c<l&&f()},a+1),e.addEventListener(u,p)}function vl(e,t){const n=window.getComputedStyle(e),s=y=>(n[y]||"").split(", "),o=s(`${Mt}Delay`),r=s(`${Mt}Duration`),i=oi(o,r),a=s(`${On}Delay`),l=s(`${On}Duration`),u=oi(a,l);let c=null,f=0,p=0;t===Mt?i>0&&(c=Mt,f=i,p=r.length):t===On?u>0&&(c=On,f=u,p=l.length):(f=Math.max(i,u),c=f>0?i>u?Mt:On:null,p=c?c===Mt?r.length:l.length:0);const g=c===Mt&&/\b(transform|all)(,|$)/.test(s(`${Mt}Property`).toString());return{type:c,timeout:f,propCount:p,hasTransform:g}}function oi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>ri(n)+ri(e[s])))}function ri(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function $o(){return document.body.offsetHeight}function xf(e,t,n){const s=e[Cn];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ii=Symbol("_vod"),Ef=Symbol("_vsh"),Rf=Symbol(""),Tf=/(^|;)\s*display\s*:/;function Af(e,t,n){const s=e.style,o=Oe(n);let r=!1;if(n&&!o){if(t)if(Oe(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&bs(s,a,"")}else for(const i in t)n[i]==null&&bs(s,i,"");for(const i in n)i==="display"&&(r=!0),bs(s,i,n[i])}else if(o){if(t!==n){const i=s[Rf];i&&(n+=";"+i),s.cssText=n,r=Tf.test(n)}}else t&&e.removeAttribute("style");ii in e&&(e[ii]=r?s.display:"",e[Ef]&&(s.display="none"))}const ai=/\s*!important$/;function bs(e,t,n){if(J(n))n.forEach(s=>bs(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Pf(e,t);ai.test(n)?e.setProperty(Qt(s),n.replace(ai,""),"important"):e[s]=n}}const li=["Webkit","Moz","ms"],_o={};function Pf(e,t){const n=_o[t];if(n)return n;let s=ot(t);if(s!=="filter"&&s in e)return _o[t]=s;s=Gs(s);for(let o=0;o<li.length;o++){const r=li[o]+s;if(r in e)return _o[t]=r}return t}const ci="http://www.w3.org/1999/xlink";function ui(e,t,n,s,o,r=Lc(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ci,t.slice(6,t.length)):e.setAttributeNS(ci,t,n):n==null||r&&!ra(n)?e.removeAttribute(t):e.setAttribute(t,r?"":wt(n)?String(n):n)}function fi(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?pl(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=ra(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function on(e,t,n,s){e.addEventListener(t,n,s)}function If(e,t,n,s){e.removeEventListener(t,n,s)}const di=Symbol("_vei");function Of(e,t,n,s,o=null){const r=e[di]||(e[di]={}),i=r[t];if(s&&i)i.value=s;else{const[a,l]=kf(t);if(s){const u=r[t]=Df(s,o);on(e,a,u,l)}else i&&(If(e,a,i,l),r[t]=void 0)}}const hi=/(?:Once|Passive|Capture)$/;function kf(e){let t;if(hi.test(e)){t={};let s;for(;s=e.match(hi);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Qt(e.slice(2)),t]}let Co=0;const Lf=Promise.resolve(),Nf=()=>Co||(Lf.then(()=>Co=0),Co=Date.now());function Df(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ut(Mf(s,n.value),t,5,[s])};return n.value=e,n.attached=Nf(),n}function Mf(e,t){if(J(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const pi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Hf=(e,t,n,s,o,r)=>{const i=o==="svg";t==="class"?xf(e,s,i):t==="style"?Af(e,n,s):Vs(t)?sr(t)||Of(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ff(e,t,s,i))?(fi(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ui(e,t,s,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Oe(s))?fi(e,ot(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ui(e,t,s,i))};function Ff(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&pi(t)&&te(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return pi(t)&&Oe(n)?!1:t in e}const bl=new WeakMap,Sl=new WeakMap,Ns=Symbol("_moveCb"),mi=Symbol("_enterCb"),Uf=e=>(delete e.props.mode,e),Vf=Uf({name:"TransitionGroup",props:Ie({},gl,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Zs(),s=Ma();let o,r;return $a(()=>{if(!o.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!Kf(o[0].el,n.vnode.el,i)){o=[];return}o.forEach(jf),o.forEach(Gf);const a=o.filter($f);$o(),a.forEach(l=>{const u=l.el,c=u.style;gt(u,i),c.transform=c.webkitTransform=c.transitionDuration="";const f=u[Ns]=p=>{p&&p.target!==u||(!p||/transform$/.test(p.propertyName))&&(u.removeEventListener("transitionend",f),u[Ns]=null,Ut(u,i))};u.addEventListener("transitionend",f)}),o=[]}),()=>{const i=ue(e),a=yl(i);let l=i.tag||Ce;if(o=[],r)for(let u=0;u<r.length;u++){const c=r[u];c.el&&c.el instanceof Element&&(o.push(c),un(c,Qn(c,a,s,n)),bl.set(c,c.el.getBoundingClientRect()))}r=t.default?mr(t.default()):[];for(let u=0;u<r.length;u++){const c=r[u];c.key!=null&&un(c,Qn(c,a,s,n))}return Te(l,null,r)}}}),Bf=Vf;function jf(e){const t=e.el;t[Ns]&&t[Ns](),t[mi]&&t[mi]()}function Gf(e){Sl.set(e,e.el.getBoundingClientRect())}function $f(e){const t=bl.get(e),n=Sl.get(e),s=t.left-n.left,o=t.top-n.top;if(s||o){const r=e.el.style;return r.transform=r.webkitTransform=`translate(${s}px,${o}px)`,r.transitionDuration="0s",e}}function Kf(e,t,n){const s=e.cloneNode(),o=e[Cn];o&&o.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const r=t.nodeType===1?t:t.parentNode;r.appendChild(s);const{hasTransform:i}=vl(s);return r.removeChild(s),i}const Ds=e=>{const t=e.props["onUpdate:modelValue"]||!1;return J(t)?n=>ms(t,n):t};function Wf(e){e.target.composing=!0}function gi(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const _n=Symbol("_assign"),rn={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[_n]=Ds(o);const r=s||o.props&&o.props.type==="number";on(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),r&&(a=Lo(a)),e[_n](a)}),n&&on(e,"change",()=>{e.value=e.value.trim()}),t||(on(e,"compositionstart",Wf),on(e,"compositionend",gi),on(e,"change",gi))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(e[_n]=Ds(i),e.composing)return;const a=(r||e.type==="number")&&!/^0\d/.test(e.value)?Lo(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===l)||(e.value=l))}},zf={deep:!0,created(e,t,n){e[_n]=Ds(n),on(e,"change",()=>{const s=e._modelValue,o=qf(e),r=e.checked,i=e[_n];if(J(s)){const a=ia(s,o),l=a!==-1;if(r&&!l)i(s.concat(o));else if(!r&&l){const u=[...s];u.splice(a,1),i(u)}}else if(Bs(s)){const a=new Set(s);r?a.add(o):a.delete(o),i(a)}else i(wl(e,r))})},mounted:yi,beforeUpdate(e,t,n){e[_n]=Ds(n),yi(e,t,n)}};function yi(e,{value:t,oldValue:n},s){e._modelValue=t;let o;if(J(t))o=ia(t,s.props.value)>-1;else if(Bs(t))o=t.has(s.props.value);else{if(t===n)return;o=Ks(t,wl(e,!0))}e.checked!==o&&(e.checked=o)}function qf(e){return"_value"in e?e._value:e.value}function wl(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Jf=["ctrl","shift","alt","meta"],Xf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Jf.some(n=>e[`${n}Key`]&&!t.includes(n))},Kt=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...r)=>{for(let i=0;i<t.length;i++){const a=Xf[t[i]];if(a&&a(o,t))return}return e(o,...r)})},Yf={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Mn=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=o=>{if(!("key"in o))return;const r=Qt(o.key);if(t.some(i=>i===r||Yf[i]===r))return e(o)})},Qf=Ie({patchProp:Hf},Sf);let vi;function Zf(){return vi||(vi=Gu(Qf))}const _l=(...e)=>{const t=Zf().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=td(s);if(!o)return;const r=t._component;!te(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,ed(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function ed(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function td(e){return Oe(e)?document.querySelector(e):e}var nd=!1;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Cl;const to=e=>Cl=e,xl=Symbol();function Ko(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Kn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Kn||(Kn={}));function sd(){const e=ua(!0),t=e.run(()=>oe({}));let n=[],s=[];const o=hr({install(r){to(o),o._a=r,r.provide(xl,o),r.config.globalProperties.$pinia=o,s.forEach(i=>n.push(i)),s=[]},use(r){return!this._a&&!nd?s.push(r):n.push(r),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}const El=()=>{};function bi(e,t,n,s=El){e.push(t);const o=()=>{const r=e.indexOf(t);r>-1&&(e.splice(r,1),s())};return!n&&fa()&&Dc(o),o}function pn(e,...t){e.slice().forEach(n=>{n(...t)})}const od=e=>e(),Si=Symbol(),xo=Symbol();function Wo(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],o=e[n];Ko(o)&&Ko(s)&&e.hasOwnProperty(n)&&!Pe(s)&&!Jt(s)?e[n]=Wo(o,s):e[n]=s}return e}const rd=Symbol();function id(e){return!Ko(e)||!e.hasOwnProperty(rd)}const{assign:Vt}=Object;function ad(e){return!!(Pe(e)&&e.effect)}function ld(e,t,n,s){const{state:o,actions:r,getters:i}=t,a=n.state.value[e];let l;function u(){a||(n.state.value[e]=o?o():{});const c=ou(n.state.value[e]);return Vt(c,r,Object.keys(i||{}).reduce((f,p)=>(f[p]=hr(Ee(()=>{to(n);const g=n._s.get(e);return i[p].call(g,g)})),f),{}))}return l=Rl(e,u,t,n,s,!0),l}function Rl(e,t,n={},s,o,r){let i;const a=Vt({actions:{}},n),l={deep:!0};let u,c,f=[],p=[],g;const y=s.state.value[e];!r&&!y&&(s.state.value[e]={}),oe({});let S;function w(q){let x;u=c=!1,typeof q=="function"?(q(s.state.value[e]),x={type:Kn.patchFunction,storeId:e,events:g}):(Wo(s.state.value[e],q),x={type:Kn.patchObject,payload:q,storeId:e,events:g});const z=S=Symbol();Pt().then(()=>{S===z&&(u=!0)}),c=!0,pn(f,x,s.state.value[e])}const k=r?function(){const{state:x}=n,z=x?x():{};this.$patch(ne=>{Vt(ne,z)})}:El;function T(){i.stop(),f=[],p=[],s._s.delete(e)}const E=(q,x="")=>{if(Si in q)return q[xo]=x,q;const z=function(){to(s);const ne=Array.from(arguments),L=[],ie=[];function we(Z){L.push(Z)}function Le(Z){ie.push(Z)}pn(p,{args:ne,name:z[xo],store:U,after:we,onError:Le});let fe;try{fe=q.apply(this&&this.$id===e?this:U,ne)}catch(Z){throw pn(ie,Z),Z}return fe instanceof Promise?fe.then(Z=>(pn(L,Z),Z)).catch(Z=>(pn(ie,Z),Promise.reject(Z))):(pn(L,fe),fe)};return z[Si]=!0,z[xo]=x,z},P={_p:s,$id:e,$onAction:bi.bind(null,p),$patch:w,$reset:k,$subscribe(q,x={}){const z=bi(f,q,x.detached,()=>ne()),ne=i.run(()=>St(()=>s.state.value[e],L=>{(x.flush==="sync"?c:u)&&q({storeId:e,type:Kn.direct,events:g},L)},Vt({},l,x)));return z},$dispose:T},U=rs(P);s._s.set(e,U);const Y=(s._a&&s._a.runWithContext||od)(()=>s._e.run(()=>(i=ua()).run(()=>t({action:E}))));for(const q in Y){const x=Y[q];if(Pe(x)&&!ad(x)||Jt(x))r||(y&&id(x)&&(Pe(x)?x.value=y[q]:Wo(x,y[q])),s.state.value[e][q]=x);else if(typeof x=="function"){const z=E(x,q);Y[q]=z,a.actions[q]=x}}return Vt(U,Y),Vt(ue(U),Y),Object.defineProperty(U,"$state",{get:()=>s.state.value[e],set:q=>{w(x=>{Vt(x,q)})}}),s._p.forEach(q=>{Vt(U,i.run(()=>q({store:U,app:s._a,pinia:s,options:a})))}),y&&r&&n.hydrate&&n.hydrate(U.$state,y),u=!0,c=!0,U}/*! #__NO_SIDE_EFFECTS__ */function Tl(e,t,n){let s,o;const r=typeof t=="function";typeof e=="string"?(s=e,o=r?n:t):(o=e,s=e.id);function i(a,l){const u=Mu();return a=a||(u?st(xl,null):null),a&&to(a),a=Cl,a._s.has(s)||(r?Rl(s,t,o,a):ld(s,o,a)),a._s.get(s)}return i.$id=s,i}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const yn=typeof document<"u";function Al(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function cd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Al(e.default)}const he=Object.assign;function Eo(e,t){const n={};for(const s in t){const o=t[s];n[s]=ft(o)?o.map(e):e(o)}return n}const Wn=()=>{},ft=Array.isArray,Pl=/#/g,ud=/&/g,fd=/\//g,dd=/=/g,hd=/\?/g,Il=/\+/g,pd=/%5B/g,md=/%5D/g,Ol=/%5E/g,gd=/%60/g,kl=/%7B/g,yd=/%7C/g,Ll=/%7D/g,vd=/%20/g;function wr(e){return encodeURI(""+e).replace(yd,"|").replace(pd,"[").replace(md,"]")}function bd(e){return wr(e).replace(kl,"{").replace(Ll,"}").replace(Ol,"^")}function zo(e){return wr(e).replace(Il,"%2B").replace(vd,"+").replace(Pl,"%23").replace(ud,"%26").replace(gd,"`").replace(kl,"{").replace(Ll,"}").replace(Ol,"^")}function Sd(e){return zo(e).replace(dd,"%3D")}function wd(e){return wr(e).replace(Pl,"%23").replace(hd,"%3F")}function _d(e){return e==null?"":wd(e).replace(fd,"%2F")}function ts(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Cd=/\/$/,xd=e=>e.replace(Cd,"");function Ro(e,t,n="/"){let s,o={},r="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),r=t.slice(l+1,a>-1?a:t.length),o=e(r)),a>-1&&(s=s||t.slice(0,a),i=t.slice(a,t.length)),s=Ad(s??t,n),{fullPath:s+(r&&"?")+r+i,path:s,query:o,hash:ts(i)}}function Ed(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function wi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Rd(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&xn(t.matched[s],n.matched[o])&&Nl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function xn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Nl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Td(e[n],t[n]))return!1;return!0}function Td(e,t){return ft(e)?_i(e,t):ft(t)?_i(t,e):e===t}function _i(e,t){return ft(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Ad(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let r=n.length-1,i,a;for(i=0;i<s.length;i++)if(a=s[i],a!==".")if(a==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(i).join("/")}const Ht={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ns;(function(e){e.pop="pop",e.push="push"})(ns||(ns={}));var zn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(zn||(zn={}));function Pd(e){if(!e)if(yn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),xd(e)}const Id=/^[^#]+#/;function Od(e,t){return e.replace(Id,"#")+t}function kd(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const no=()=>({left:window.scrollX,top:window.scrollY});function Ld(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=kd(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ci(e,t){return(history.state?history.state.position-t:-1)+e}const qo=new Map;function Nd(e,t){qo.set(e,t)}function Dd(e){const t=qo.get(e);return qo.delete(e),t}let Md=()=>location.protocol+"//"+location.host;function Dl(e,t){const{pathname:n,search:s,hash:o}=t,r=e.indexOf("#");if(r>-1){let a=o.includes(e.slice(r))?e.slice(r).length:1,l=o.slice(a);return l[0]!=="/"&&(l="/"+l),wi(l,"")}return wi(n,e)+s+o}function Hd(e,t,n,s){let o=[],r=[],i=null;const a=({state:p})=>{const g=Dl(e,location),y=n.value,S=t.value;let w=0;if(p){if(n.value=g,t.value=p,i&&i===y){i=null;return}w=S?p.position-S.position:0}else s(g);o.forEach(k=>{k(n.value,y,{delta:w,type:ns.pop,direction:w?w>0?zn.forward:zn.back:zn.unknown})})};function l(){i=n.value}function u(p){o.push(p);const g=()=>{const y=o.indexOf(p);y>-1&&o.splice(y,1)};return r.push(g),g}function c(){const{history:p}=window;p.state&&p.replaceState(he({},p.state,{scroll:no()}),"")}function f(){for(const p of r)p();r=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function xi(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?no():null}}function Fd(e){const{history:t,location:n}=window,s={value:Dl(e,n)},o={value:t.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(l,u,c){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:Md()+e+l;try{t[c?"replaceState":"pushState"](u,"",p),o.value=u}catch(g){console.error(g),n[c?"replace":"assign"](p)}}function i(l,u){const c=he({},t.state,xi(o.value.back,l,o.value.forward,!0),u,{position:o.value.position});r(l,c,!0),s.value=l}function a(l,u){const c=he({},o.value,t.state,{forward:l,scroll:no()});r(c.current,c,!0);const f=he({},xi(s.value,l,null),{position:c.position+1},u);r(l,f,!1),s.value=l}return{location:s,state:o,push:a,replace:i}}function Ud(e){e=Pd(e);const t=Fd(e),n=Hd(e,t.state,t.location,t.replace);function s(r,i=!0){i||n.pauseListeners(),history.go(r)}const o=he({location:"",base:e,go:s,createHref:Od.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Vd(e){return typeof e=="string"||e&&typeof e=="object"}function Ml(e){return typeof e=="string"||typeof e=="symbol"}const Hl=Symbol("");var Ei;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ei||(Ei={}));function En(e,t){return he(new Error,{type:e,[Hl]:!0},t)}function xt(e,t){return e instanceof Error&&Hl in e&&(t==null||!!(e.type&t))}const Ri="[^/]+?",Bd={sensitive:!1,strict:!1,start:!0,end:!0},jd=/[.+*?^${}()[\]/\\]/g;function Gd(e,t){const n=he({},Bd,t),s=[];let o=n.start?"^":"";const r=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const p=u[f];let g=40+(n.sensitive?.25:0);if(p.type===0)f||(o+="/"),o+=p.value.replace(jd,"\\$&"),g+=40;else if(p.type===1){const{value:y,repeatable:S,optional:w,regexp:k}=p;r.push({name:y,repeatable:S,optional:w});const T=k||Ri;if(T!==Ri){g+=10;try{new RegExp(`(${T})`)}catch(P){throw new Error(`Invalid custom RegExp for param "${y}" (${T}): `+P.message)}}let E=S?`((?:${T})(?:/(?:${T}))*)`:`(${T})`;f||(E=w&&u.length<2?`(?:/${E})`:"/"+E),w&&(E+="?"),o+=E,g+=20,w&&(g+=-8),S&&(g+=-20),T===".*"&&(g+=-50)}c.push(g)}s.push(c)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function a(u){const c=u.match(i),f={};if(!c)return null;for(let p=1;p<c.length;p++){const g=c[p]||"",y=r[p-1];f[y.name]=g&&y.repeatable?g.split("/"):g}return f}function l(u){let c="",f=!1;for(const p of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const g of p)if(g.type===0)c+=g.value;else if(g.type===1){const{value:y,repeatable:S,optional:w}=g,k=y in u?u[y]:"";if(ft(k)&&!S)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const T=ft(k)?k.join("/"):k;if(!T)if(w)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);c+=T}}return c||"/"}return{re:i,score:s,keys:r,parse:a,stringify:l}}function $d(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Fl(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const r=$d(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(Ti(s))return 1;if(Ti(o))return-1}return o.length-s.length}function Ti(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Kd={type:0,value:""},Wd=/[a-zA-Z0-9_]/;function zd(e){if(!e)return[[]];if(e==="/")return[[Kd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,s=n;const o=[];let r;function i(){r&&o.push(r),r=[]}let a=0,l,u="",c="";function f(){u&&(n===0?r.push({type:0,value:u}):n===1||n===2||n===3?(r.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(u&&f(),i()):l===":"?(f(),n=1):p();break;case 4:p(),n=s;break;case 1:l==="("?n=2:Wd.test(l)?p():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),o}function qd(e,t,n){const s=Gd(zd(e.path),n),o=he(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Jd(e,t){const n=[],s=new Map;t=Oi({strict:!1,end:!0,sensitive:!1},t);function o(f){return s.get(f)}function r(f,p,g){const y=!g,S=Pi(f);S.aliasOf=g&&g.record;const w=Oi(t,f),k=[S];if("alias"in f){const P=typeof f.alias=="string"?[f.alias]:f.alias;for(const U of P)k.push(Pi(he({},S,{components:g?g.record.components:S.components,path:U,aliasOf:g?g.record:S})))}let T,E;for(const P of k){const{path:U}=P;if(p&&U[0]!=="/"){const Q=p.record.path,Y=Q[Q.length-1]==="/"?"":"/";P.path=p.record.path+(U&&Y+U)}if(T=qd(P,p,w),g?g.alias.push(T):(E=E||T,E!==T&&E.alias.push(T),y&&f.name&&!Ii(T)&&i(f.name)),Ul(T)&&l(T),S.children){const Q=S.children;for(let Y=0;Y<Q.length;Y++)r(Q[Y],T,g&&g.children[Y])}g=g||T}return E?()=>{i(E)}:Wn}function i(f){if(Ml(f)){const p=s.get(f);p&&(s.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return n}function l(f){const p=Qd(f,n);n.splice(p,0,f),f.record.name&&!Ii(f)&&s.set(f.record.name,f)}function u(f,p){let g,y={},S,w;if("name"in f&&f.name){if(g=s.get(f.name),!g)throw En(1,{location:f});w=g.record.name,y=he(Ai(p.params,g.keys.filter(E=>!E.optional).concat(g.parent?g.parent.keys.filter(E=>E.optional):[]).map(E=>E.name)),f.params&&Ai(f.params,g.keys.map(E=>E.name))),S=g.stringify(y)}else if(f.path!=null)S=f.path,g=n.find(E=>E.re.test(S)),g&&(y=g.parse(S),w=g.record.name);else{if(g=p.name?s.get(p.name):n.find(E=>E.re.test(p.path)),!g)throw En(1,{location:f,currentLocation:p});w=g.record.name,y=he({},p.params,f.params),S=g.stringify(y)}const k=[];let T=g;for(;T;)k.unshift(T.record),T=T.parent;return{name:w,path:S,params:y,matched:k,meta:Yd(k)}}e.forEach(f=>r(f));function c(){n.length=0,s.clear()}return{addRoute:r,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:o}}function Ai(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Pi(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Xd(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Xd(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Ii(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Yd(e){return e.reduce((t,n)=>he(t,n.meta),{})}function Oi(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Qd(e,t){let n=0,s=t.length;for(;n!==s;){const r=n+s>>1;Fl(e,t[r])<0?s=r:n=r+1}const o=Zd(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function Zd(e){let t=e;for(;t=t.parent;)if(Ul(t)&&Fl(e,t)===0)return t}function Ul({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function eh(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<s.length;++o){const r=s[o].replace(Il," "),i=r.indexOf("="),a=ts(i<0?r:r.slice(0,i)),l=i<0?null:ts(r.slice(i+1));if(a in t){let u=t[a];ft(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function ki(e){let t="";for(let n in e){const s=e[n];if(n=Sd(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(ft(s)?s.map(r=>r&&zo(r)):[s&&zo(s)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+n,r!=null&&(t+="="+r))})}return t}function th(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=ft(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const nh=Symbol(""),Li=Symbol(""),so=Symbol(""),_r=Symbol(""),Jo=Symbol("");function kn(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function $t(e,t,n,s,o,r=i=>i()){const i=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((a,l)=>{const u=p=>{p===!1?l(En(4,{from:n,to:t})):p instanceof Error?l(p):Vd(p)?l(En(2,{from:t,to:p})):(i&&s.enterCallbacks[o]===i&&typeof p=="function"&&i.push(p),a())},c=r(()=>e.call(s&&s.instances[o],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(p=>l(p))})}function To(e,t,n,s,o=r=>r()){const r=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Al(l)){const c=(l.__vccOpts||l)[t];c&&r.push($t(c,n,s,i,a,o))}else{let u=l();r.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=cd(c)?c.default:c;i.mods[a]=c,i.components[a]=f;const g=(f.__vccOpts||f)[t];return g&&$t(g,n,s,i,a,o)()}))}}return r}function Ni(e){const t=st(so),n=st(_r),s=Ee(()=>{const l=Sn(e.to);return t.resolve(l)}),o=Ee(()=>{const{matched:l}=s.value,{length:u}=l,c=l[u-1],f=n.matched;if(!c||!f.length)return-1;const p=f.findIndex(xn.bind(null,c));if(p>-1)return p;const g=Di(l[u-2]);return u>1&&Di(c)===g&&f[f.length-1].path!==g?f.findIndex(xn.bind(null,l[u-2])):p}),r=Ee(()=>o.value>-1&&ah(n.params,s.value.params)),i=Ee(()=>o.value>-1&&o.value===n.matched.length-1&&Nl(n.params,s.value.params));function a(l={}){if(ih(l)){const u=t[Sn(e.replace)?"replace":"push"](Sn(e.to)).catch(Wn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:Ee(()=>s.value.href),isActive:r,isExactActive:i,navigate:a}}function sh(e){return e.length===1?e[0]:e}const oh=Ba({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ni,setup(e,{slots:t}){const n=rs(Ni(e)),{options:s}=st(so),o=Ee(()=>({[Mi(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Mi(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&sh(t.default(n));return e.custom?r:Sr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),rh=oh;function ih(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function ah(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!ft(o)||o.length!==s.length||s.some((r,i)=>r!==o[i]))return!1}return!0}function Di(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Mi=(e,t,n)=>e??t??n,lh=Ba({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=st(Jo),o=Ee(()=>e.route||s.value),r=st(Li,0),i=Ee(()=>{let u=Sn(r);const{matched:c}=o.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),a=Ee(()=>o.value.matched[i.value]);gs(Li,Ee(()=>i.value+1)),gs(nh,a),gs(Jo,o);const l=oe();return St(()=>[l.value,a.value,e.name],([u,c,f],[p,g,y])=>{c&&(c.instances[f]=u,g&&g!==c&&u&&u===p&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),u&&c&&(!g||!xn(c,g)||!p)&&(c.enterCallbacks[f]||[]).forEach(S=>S(u))},{flush:"post"}),()=>{const u=o.value,c=e.name,f=a.value,p=f&&f.components[c];if(!p)return Hi(n.default,{Component:p,route:u});const g=f.props[c],y=g?g===!0?u.params:typeof g=="function"?g(u):g:null,w=Sr(p,he({},y,t,{onVnodeUnmounted:k=>{k.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return Hi(n.default,{Component:w,route:u})||w}}});function Hi(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ch=lh;function uh(e){const t=Jd(e.routes,e),n=e.parseQuery||eh,s=e.stringifyQuery||ki,o=e.history,r=kn(),i=kn(),a=kn(),l=tu(Ht);let u=Ht;yn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Eo.bind(null,C=>""+C),f=Eo.bind(null,_d),p=Eo.bind(null,ts);function g(C,B){let H,K;return Ml(C)?(H=t.getRecordMatcher(C),K=B):K=C,t.addRoute(K,H)}function y(C){const B=t.getRecordMatcher(C);B&&t.removeRoute(B)}function S(){return t.getRoutes().map(C=>C.record)}function w(C){return!!t.getRecordMatcher(C)}function k(C,B){if(B=he({},B||l.value),typeof C=="string"){const m=Ro(n,C,B.path),v=t.resolve({path:m.path},B),R=o.createHref(m.fullPath);return he(m,v,{params:p(v.params),hash:ts(m.hash),redirectedFrom:void 0,href:R})}let H;if(C.path!=null)H=he({},C,{path:Ro(n,C.path,B.path).path});else{const m=he({},C.params);for(const v in m)m[v]==null&&delete m[v];H=he({},C,{params:f(m)}),B.params=f(B.params)}const K=t.resolve(H,B),de=C.hash||"";K.params=c(p(K.params));const _e=Ed(s,he({},C,{hash:bd(de),path:K.path})),h=o.createHref(_e);return he({fullPath:_e,hash:de,query:s===ki?th(C.query):C.query||{}},K,{redirectedFrom:void 0,href:h})}function T(C){return typeof C=="string"?Ro(n,C,l.value.path):he({},C)}function E(C,B){if(u!==C)return En(8,{from:B,to:C})}function P(C){return Y(C)}function U(C){return P(he(T(C),{replace:!0}))}function Q(C){const B=C.matched[C.matched.length-1];if(B&&B.redirect){const{redirect:H}=B;let K=typeof H=="function"?H(C):H;return typeof K=="string"&&(K=K.includes("?")||K.includes("#")?K=T(K):{path:K},K.params={}),he({query:C.query,hash:C.hash,params:K.path!=null?{}:C.params},K)}}function Y(C,B){const H=u=k(C),K=l.value,de=C.state,_e=C.force,h=C.replace===!0,m=Q(H);if(m)return Y(he(T(m),{state:typeof m=="object"?he({},de,m.state):de,force:_e,replace:h}),B||H);const v=H;v.redirectedFrom=B;let R;return!_e&&Rd(s,K,H)&&(R=En(16,{to:v,from:K}),Ae(K,K,!0,!1)),(R?Promise.resolve(R):z(v,K)).catch(_=>xt(_)?xt(_,2)?_:le(_):ae(_,v,K)).then(_=>{if(_){if(xt(_,2))return Y(he({replace:h},T(_.to),{state:typeof _.to=="object"?he({},de,_.to.state):de,force:_e}),B||v)}else _=L(v,K,!0,h,de);return ne(v,K,_),_})}function q(C,B){const H=E(C,B);return H?Promise.reject(H):Promise.resolve()}function x(C){const B=me.values().next().value;return B&&typeof B.runWithContext=="function"?B.runWithContext(C):C()}function z(C,B){let H;const[K,de,_e]=fh(C,B);H=To(K.reverse(),"beforeRouteLeave",C,B);for(const m of K)m.leaveGuards.forEach(v=>{H.push($t(v,C,B))});const h=q.bind(null,C,B);return H.push(h),De(H).then(()=>{H=[];for(const m of r.list())H.push($t(m,C,B));return H.push(h),De(H)}).then(()=>{H=To(de,"beforeRouteUpdate",C,B);for(const m of de)m.updateGuards.forEach(v=>{H.push($t(v,C,B))});return H.push(h),De(H)}).then(()=>{H=[];for(const m of _e)if(m.beforeEnter)if(ft(m.beforeEnter))for(const v of m.beforeEnter)H.push($t(v,C,B));else H.push($t(m.beforeEnter,C,B));return H.push(h),De(H)}).then(()=>(C.matched.forEach(m=>m.enterCallbacks={}),H=To(_e,"beforeRouteEnter",C,B,x),H.push(h),De(H))).then(()=>{H=[];for(const m of i.list())H.push($t(m,C,B));return H.push(h),De(H)}).catch(m=>xt(m,8)?m:Promise.reject(m))}function ne(C,B,H){a.list().forEach(K=>x(()=>K(C,B,H)))}function L(C,B,H,K,de){const _e=E(C,B);if(_e)return _e;const h=B===Ht,m=yn?history.state:{};H&&(K||h?o.replace(C.fullPath,he({scroll:h&&m&&m.scroll},de)):o.push(C.fullPath,de)),l.value=C,Ae(C,B,H,h),le()}let ie;function we(){ie||(ie=o.listen((C,B,H)=>{if(!Dt.listening)return;const K=k(C),de=Q(K);if(de){Y(he(de,{replace:!0,force:!0}),K).catch(Wn);return}u=K;const _e=l.value;yn&&Nd(Ci(_e.fullPath,H.delta),no()),z(K,_e).catch(h=>xt(h,12)?h:xt(h,2)?(Y(he(T(h.to),{force:!0}),K).then(m=>{xt(m,20)&&!H.delta&&H.type===ns.pop&&o.go(-1,!1)}).catch(Wn),Promise.reject()):(H.delta&&o.go(-H.delta,!1),ae(h,K,_e))).then(h=>{h=h||L(K,_e,!1),h&&(H.delta&&!xt(h,8)?o.go(-H.delta,!1):H.type===ns.pop&&xt(h,20)&&o.go(-1,!1)),ne(K,_e,h)}).catch(Wn)}))}let Le=kn(),fe=kn(),Z;function ae(C,B,H){le(C);const K=fe.list();return K.length?K.forEach(de=>de(C,B,H)):console.error(C),Promise.reject(C)}function V(){return Z&&l.value!==Ht?Promise.resolve():new Promise((C,B)=>{Le.add([C,B])})}function le(C){return Z||(Z=!C,we(),Le.list().forEach(([B,H])=>C?H(C):B()),Le.reset()),C}function Ae(C,B,H,K){const{scrollBehavior:de}=e;if(!yn||!de)return Promise.resolve();const _e=!H&&Dd(Ci(C.fullPath,0))||(K||!H)&&history.state&&history.state.scroll||null;return Pt().then(()=>de(C,B,_e)).then(h=>h&&Ld(h)).catch(h=>ae(h,C,B))}const I=C=>o.go(C);let $;const me=new Set,Dt={currentRoute:l,listening:!0,addRoute:g,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:S,resolve:k,options:e,push:P,replace:U,go:I,back:()=>I(-1),forward:()=>I(1),beforeEach:r.add,beforeResolve:i.add,afterEach:a.add,onError:fe.add,isReady:V,install(C){const B=this;C.component("RouterLink",rh),C.component("RouterView",ch),C.config.globalProperties.$router=B,Object.defineProperty(C.config.globalProperties,"$route",{enumerable:!0,get:()=>Sn(l)}),yn&&!$&&l.value===Ht&&($=!0,P(o.location).catch(de=>{}));const H={};for(const de in Ht)Object.defineProperty(H,de,{get:()=>l.value[de],enumerable:!0});C.provide(so,B),C.provide(_r,Ra(H)),C.provide(Jo,l);const K=C.unmount;me.add(C),C.unmount=function(){me.delete(C),me.size<1&&(u=Ht,ie&&ie(),ie=null,l.value=Ht,$=!1,Z=!1),K()}}};function De(C){return C.reduce((B,H)=>B.then(()=>x(H)),Promise.resolve())}return Dt}function fh(e,t){const n=[],s=[],o=[],r=Math.max(t.matched.length,e.matched.length);for(let i=0;i<r;i++){const a=t.matched[i];a&&(e.matched.find(u=>xn(u,a))?s.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(u=>xn(u,l))||o.push(l))}return[n,s,o]}function Cr(){return st(so)}function Vl(e){return st(_r)}const Nt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},dh={name:"App"},hh={id:"app"};function ph(e,t,n,s,o,r){const i=Is("router-view");return j(),G("div",hh,[Te(i)])}const mh=Nt(dh,[["render",ph]]),gh="modulepreload",yh=function(e){return"/"+e},Fi={},Ms=function(t,n,s){if(!n||n.length===0)return t();const o=document.getElementsByTagName("link");return Promise.all(n.map(r=>{if(r=yh(r),r in Fi)return;Fi[r]=!0;const i=r.endsWith(".css"),a=i?'[rel="stylesheet"]':"";if(!!s)for(let c=o.length-1;c>=0;c--){const f=o[c];if(f.href===r&&(!i||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${r}"]${a}`))return;const u=document.createElement("link");if(u.rel=i?"stylesheet":gh,i||(u.as="script",u.crossOrigin=""),u.href=r,document.head.appendChild(u),i)return new Promise((c,f)=>{u.addEventListener("load",c),u.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${r}`)))})})).then(()=>t()).catch(r=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=r,window.dispatchEvent(i),!i.defaultPrevented)throw r})};function Bl(e,t){return function(){return e.apply(t,arguments)}}const{toString:vh}=Object.prototype,{getPrototypeOf:xr}=Object,{iterator:oo,toStringTag:jl}=Symbol,ro=(e=>t=>{const n=vh.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),dt=e=>(e=e.toLowerCase(),t=>ro(t)===e),io=e=>t=>typeof t===e,{isArray:Tn}=Array,ss=io("undefined");function ls(e){return e!==null&&!ss(e)&&e.constructor!==null&&!ss(e.constructor)&&ze(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Gl=dt("ArrayBuffer");function bh(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Gl(e.buffer),t}const Sh=io("string"),ze=io("function"),$l=io("number"),cs=e=>e!==null&&typeof e=="object",wh=e=>e===!0||e===!1,Ss=e=>{if(ro(e)!=="object")return!1;const t=xr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(jl in e)&&!(oo in e)},_h=e=>{if(!cs(e)||ls(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Ch=dt("Date"),xh=dt("File"),Eh=dt("Blob"),Rh=dt("FileList"),Th=e=>cs(e)&&ze(e.pipe),Ah=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ze(e.append)&&((t=ro(e))==="formdata"||t==="object"&&ze(e.toString)&&e.toString()==="[object FormData]"))},Ph=dt("URLSearchParams"),[Ih,Oh,kh,Lh]=["ReadableStream","Request","Response","Headers"].map(dt),Nh=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function us(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,o;if(typeof e!="object"&&(e=[e]),Tn(e))for(s=0,o=e.length;s<o;s++)t.call(null,e[s],s,e);else{if(ls(e))return;const r=n?Object.getOwnPropertyNames(e):Object.keys(e),i=r.length;let a;for(s=0;s<i;s++)a=r[s],t.call(null,e[a],a,e)}}function Kl(e,t){if(ls(e))return null;t=t.toLowerCase();const n=Object.keys(e);let s=n.length,o;for(;s-- >0;)if(o=n[s],t===o.toLowerCase())return o;return null}const an=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Wl=e=>!ss(e)&&e!==an;function Xo(){const{caseless:e}=Wl(this)&&this||{},t={},n=(s,o)=>{const r=e&&Kl(t,o)||o;Ss(t[r])&&Ss(s)?t[r]=Xo(t[r],s):Ss(s)?t[r]=Xo({},s):Tn(s)?t[r]=s.slice():t[r]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&us(arguments[s],n);return t}const Dh=(e,t,n,{allOwnKeys:s}={})=>(us(t,(o,r)=>{n&&ze(o)?e[r]=Bl(o,n):e[r]=o},{allOwnKeys:s}),e),Mh=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Hh=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Fh=(e,t,n,s)=>{let o,r,i;const a={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),r=o.length;r-- >0;)i=o[r],(!s||s(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&xr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Uh=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Vh=e=>{if(!e)return null;if(Tn(e))return e;let t=e.length;if(!$l(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Bh=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&xr(Uint8Array)),jh=(e,t)=>{const s=(e&&e[oo]).call(e);let o;for(;(o=s.next())&&!o.done;){const r=o.value;t.call(e,r[0],r[1])}},Gh=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},$h=dt("HTMLFormElement"),Kh=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,o){return s.toUpperCase()+o}),Ui=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Wh=dt("RegExp"),zl=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};us(n,(o,r)=>{let i;(i=t(o,r,e))!==!1&&(s[r]=i||o)}),Object.defineProperties(e,s)},zh=e=>{zl(e,(t,n)=>{if(ze(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(ze(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},qh=(e,t)=>{const n={},s=o=>{o.forEach(r=>{n[r]=!0})};return Tn(e)?s(e):s(String(e).split(t)),n},Jh=()=>{},Xh=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Yh(e){return!!(e&&ze(e.append)&&e[jl]==="FormData"&&e[oo])}const Qh=e=>{const t=new Array(10),n=(s,o)=>{if(cs(s)){if(t.indexOf(s)>=0)return;if(ls(s))return s;if(!("toJSON"in s)){t[o]=s;const r=Tn(s)?[]:{};return us(s,(i,a)=>{const l=n(i,o+1);!ss(l)&&(r[a]=l)}),t[o]=void 0,r}}return s};return n(e,0)},Zh=dt("AsyncFunction"),ep=e=>e&&(cs(e)||ze(e))&&ze(e.then)&&ze(e.catch),ql=((e,t)=>e?setImmediate:t?((n,s)=>(an.addEventListener("message",({source:o,data:r})=>{o===an&&r===n&&s.length&&s.shift()()},!1),o=>{s.push(o),an.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",ze(an.postMessage)),tp=typeof queueMicrotask<"u"?queueMicrotask.bind(an):typeof process<"u"&&process.nextTick||ql,np=e=>e!=null&&ze(e[oo]),b={isArray:Tn,isArrayBuffer:Gl,isBuffer:ls,isFormData:Ah,isArrayBufferView:bh,isString:Sh,isNumber:$l,isBoolean:wh,isObject:cs,isPlainObject:Ss,isEmptyObject:_h,isReadableStream:Ih,isRequest:Oh,isResponse:kh,isHeaders:Lh,isUndefined:ss,isDate:Ch,isFile:xh,isBlob:Eh,isRegExp:Wh,isFunction:ze,isStream:Th,isURLSearchParams:Ph,isTypedArray:Bh,isFileList:Rh,forEach:us,merge:Xo,extend:Dh,trim:Nh,stripBOM:Mh,inherits:Hh,toFlatObject:Fh,kindOf:ro,kindOfTest:dt,endsWith:Uh,toArray:Vh,forEachEntry:jh,matchAll:Gh,isHTMLForm:$h,hasOwnProperty:Ui,hasOwnProp:Ui,reduceDescriptors:zl,freezeMethods:zh,toObjectSet:qh,toCamelCase:Kh,noop:Jh,toFiniteNumber:Xh,findKey:Kl,global:an,isContextDefined:Wl,isSpecCompliantForm:Yh,toJSONObject:Qh,isAsyncFn:Zh,isThenable:ep,setImmediate:ql,asap:tp,isIterable:np};function re(e,t,n,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}b.inherits(re,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const Jl=re.prototype,Xl={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Xl[e]={value:e}});Object.defineProperties(re,Xl);Object.defineProperty(Jl,"isAxiosError",{value:!0});re.from=(e,t,n,s,o,r)=>{const i=Object.create(Jl);return b.toFlatObject(e,i,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),re.call(i,e.message,t,n,s,o),i.cause=e,i.name=e.name,r&&Object.assign(i,r),i};const sp=null;function Yo(e){return b.isPlainObject(e)||b.isArray(e)}function Yl(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function Vi(e,t,n){return e?e.concat(t).map(function(o,r){return o=Yl(o),!n&&r?"["+o+"]":o}).join(n?".":""):t}function op(e){return b.isArray(e)&&!e.some(Yo)}const rp=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function ao(e,t,n){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=b.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,w){return!b.isUndefined(w[S])});const s=n.metaTokens,o=n.visitor||c,r=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(o))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(b.isDate(y))return y.toISOString();if(b.isBoolean(y))return y.toString();if(!l&&b.isBlob(y))throw new re("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(y)||b.isTypedArray(y)?l&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function c(y,S,w){let k=y;if(y&&!w&&typeof y=="object"){if(b.endsWith(S,"{}"))S=s?S:S.slice(0,-2),y=JSON.stringify(y);else if(b.isArray(y)&&op(y)||(b.isFileList(y)||b.endsWith(S,"[]"))&&(k=b.toArray(y)))return S=Yl(S),k.forEach(function(E,P){!(b.isUndefined(E)||E===null)&&t.append(i===!0?Vi([S],P,r):i===null?S:S+"[]",u(E))}),!1}return Yo(y)?!0:(t.append(Vi(w,S,r),u(y)),!1)}const f=[],p=Object.assign(rp,{defaultVisitor:c,convertValue:u,isVisitable:Yo});function g(y,S){if(!b.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+S.join("."));f.push(y),b.forEach(y,function(k,T){(!(b.isUndefined(k)||k===null)&&o.call(t,k,b.isString(T)?T.trim():T,S,p))===!0&&g(k,S?S.concat(T):[T])}),f.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Bi(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Er(e,t){this._pairs=[],e&&ao(e,this,t)}const Ql=Er.prototype;Ql.append=function(t,n){this._pairs.push([t,n])};Ql.toString=function(t){const n=t?function(s){return t.call(this,s,Bi)}:Bi;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function ip(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Zl(e,t,n){if(!t)return e;const s=n&&n.encode||ip;b.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let r;if(o?r=o(t,n):r=b.isURLSearchParams(t)?t.toString():new Er(t,n).toString(s),r){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+r}return e}class ap{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(s){s!==null&&t(s)})}}const ji=ap,ec={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},lp=typeof URLSearchParams<"u"?URLSearchParams:Er,cp=typeof FormData<"u"?FormData:null,up=typeof Blob<"u"?Blob:null,fp={isBrowser:!0,classes:{URLSearchParams:lp,FormData:cp,Blob:up},protocols:["http","https","file","blob","url","data"]},Rr=typeof window<"u"&&typeof document<"u",Qo=typeof navigator=="object"&&navigator||void 0,dp=Rr&&(!Qo||["ReactNative","NativeScript","NS"].indexOf(Qo.product)<0),hp=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),pp=Rr&&window.location.href||"http://localhost",mp=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Rr,hasStandardBrowserEnv:dp,hasStandardBrowserWebWorkerEnv:hp,navigator:Qo,origin:pp},Symbol.toStringTag,{value:"Module"})),Ve={...mp,...fp};function gp(e,t){return ao(e,new Ve.classes.URLSearchParams,{visitor:function(n,s,o,r){return Ve.isNode&&b.isBuffer(n)?(this.append(s,n.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)},...t})}function yp(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function vp(e){const t={},n=Object.keys(e);let s;const o=n.length;let r;for(s=0;s<o;s++)r=n[s],t[r]=e[r];return t}function tc(e){function t(n,s,o,r){let i=n[r++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),l=r>=n.length;return i=!i&&b.isArray(o)?o.length:i,l?(b.hasOwnProp(o,i)?o[i]=[o[i],s]:o[i]=s,!a):((!o[i]||!b.isObject(o[i]))&&(o[i]=[]),t(n,s,o[i],r)&&b.isArray(o[i])&&(o[i]=vp(o[i])),!a)}if(b.isFormData(e)&&b.isFunction(e.entries)){const n={};return b.forEachEntry(e,(s,o)=>{t(yp(s),o,n,0)}),n}return null}function bp(e,t,n){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Tr={transitional:ec,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",o=s.indexOf("application/json")>-1,r=b.isObject(t);if(r&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return o?JSON.stringify(tc(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(r){if(s.indexOf("application/x-www-form-urlencoded")>-1)return gp(t,this.formSerializer).toString();if((a=b.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return ao(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return r||o?(n.setContentType("application/json",!1),bp(t)):t}],transformResponse:[function(t){const n=this.transitional||Tr.transitional,s=n&&n.forcedJSONParsing,o=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(s&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?re.from(a,re.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ve.classes.FormData,Blob:Ve.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{Tr.headers[e]={}});const Ar=Tr,Sp=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),wp=e=>{const t={};let n,s,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),s=i.substring(o+1).trim(),!(!n||t[n]&&Sp[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Gi=Symbol("internals");function Ln(e){return e&&String(e).trim().toLowerCase()}function ws(e){return e===!1||e==null?e:b.isArray(e)?e.map(ws):String(e)}function _p(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Cp=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ao(e,t,n,s,o){if(b.isFunction(s))return s.call(this,t,n);if(o&&(t=n),!!b.isString(t)){if(b.isString(s))return t.indexOf(s)!==-1;if(b.isRegExp(s))return s.test(t)}}function xp(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Ep(e,t){const n=b.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(o,r,i){return this[s].call(this,t,o,r,i)},configurable:!0})})}class lo{constructor(t){t&&this.set(t)}set(t,n,s){const o=this;function r(a,l,u){const c=Ln(l);if(!c)throw new Error("header name must be a non-empty string");const f=b.findKey(o,c);(!f||o[f]===void 0||u===!0||u===void 0&&o[f]!==!1)&&(o[f||l]=ws(a))}const i=(a,l)=>b.forEach(a,(u,c)=>r(u,c,l));if(b.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(b.isString(t)&&(t=t.trim())&&!Cp(t))i(wp(t),n);else if(b.isObject(t)&&b.isIterable(t)){let a={},l,u;for(const c of t){if(!b.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[u=c[0]]=(l=a[u])?b.isArray(l)?[...l,c[1]]:[l,c[1]]:c[1]}i(a,n)}else t!=null&&r(n,t,s);return this}get(t,n){if(t=Ln(t),t){const s=b.findKey(this,t);if(s){const o=this[s];if(!n)return o;if(n===!0)return _p(o);if(b.isFunction(n))return n.call(this,o,s);if(b.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ln(t),t){const s=b.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Ao(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let o=!1;function r(i){if(i=Ln(i),i){const a=b.findKey(s,i);a&&(!n||Ao(s,s[a],a,n))&&(delete s[a],o=!0)}}return b.isArray(t)?t.forEach(r):r(t),o}clear(t){const n=Object.keys(this);let s=n.length,o=!1;for(;s--;){const r=n[s];(!t||Ao(this,this[r],r,t,!0))&&(delete this[r],o=!0)}return o}normalize(t){const n=this,s={};return b.forEach(this,(o,r)=>{const i=b.findKey(s,r);if(i){n[i]=ws(o),delete n[r];return}const a=t?xp(r):String(r).trim();a!==r&&delete n[r],n[a]=ws(o),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return b.forEach(this,(s,o)=>{s!=null&&s!==!1&&(n[o]=t&&b.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(o=>s.set(o)),s}static accessor(t){const s=(this[Gi]=this[Gi]={accessors:{}}).accessors,o=this.prototype;function r(i){const a=Ln(i);s[a]||(Ep(o,i),s[a]=!0)}return b.isArray(t)?t.forEach(r):r(t),this}}lo.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(lo.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});b.freezeMethods(lo);const ct=lo;function Po(e,t){const n=this||Ar,s=t||n,o=ct.from(s.headers);let r=s.data;return b.forEach(e,function(a){r=a.call(n,r,o.normalize(),t?t.status:void 0)}),o.normalize(),r}function nc(e){return!!(e&&e.__CANCEL__)}function An(e,t,n){re.call(this,e??"canceled",re.ERR_CANCELED,t,n),this.name="CanceledError"}b.inherits(An,re,{__CANCEL__:!0});function sc(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new re("Request failed with status code "+n.status,[re.ERR_BAD_REQUEST,re.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Rp(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Tp(e,t){e=e||10;const n=new Array(e),s=new Array(e);let o=0,r=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=s[r];i||(i=u),n[o]=l,s[o]=u;let f=r,p=0;for(;f!==o;)p+=n[f++],f=f%e;if(o=(o+1)%e,o===r&&(r=(r+1)%e),u-i<t)return;const g=c&&u-c;return g?Math.round(p*1e3/g):void 0}}function Ap(e,t){let n=0,s=1e3/t,o,r;const i=(u,c=Date.now())=>{n=c,o=null,r&&(clearTimeout(r),r=null),e(...u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=s?i(u,c):(o=u,r||(r=setTimeout(()=>{r=null,i(o)},s-f)))},()=>o&&i(o)]}const Hs=(e,t,n=3)=>{let s=0;const o=Tp(50,250);return Ap(r=>{const i=r.loaded,a=r.lengthComputable?r.total:void 0,l=i-s,u=o(l),c=i<=a;s=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&c?(a-i)/u:void 0,event:r,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},$i=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Ki=e=>(...t)=>b.asap(()=>e(...t)),Pp=Ve.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ve.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ve.origin),Ve.navigator&&/(msie|trident)/i.test(Ve.navigator.userAgent)):()=>!0,Ip=Ve.hasStandardBrowserEnv?{write(e,t,n,s,o,r){const i=[e+"="+encodeURIComponent(t)];b.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),b.isString(s)&&i.push("path="+s),b.isString(o)&&i.push("domain="+o),r===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Op(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function kp(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function oc(e,t,n){let s=!Op(t);return e&&(s||n==!1)?kp(e,t):t}const Wi=e=>e instanceof ct?{...e}:e;function fn(e,t){t=t||{};const n={};function s(u,c,f,p){return b.isPlainObject(u)&&b.isPlainObject(c)?b.merge.call({caseless:p},u,c):b.isPlainObject(c)?b.merge({},c):b.isArray(c)?c.slice():c}function o(u,c,f,p){if(b.isUndefined(c)){if(!b.isUndefined(u))return s(void 0,u,f,p)}else return s(u,c,f,p)}function r(u,c){if(!b.isUndefined(c))return s(void 0,c)}function i(u,c){if(b.isUndefined(c)){if(!b.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function a(u,c,f){if(f in t)return s(u,c);if(f in e)return s(void 0,u)}const l={url:r,method:r,data:r,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(u,c,f)=>o(Wi(u),Wi(c),f,!0)};return b.forEach(Object.keys({...e,...t}),function(c){const f=l[c]||o,p=f(e[c],t[c],c);b.isUndefined(p)&&f!==a||(n[c]=p)}),n}const rc=e=>{const t=fn({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:r,headers:i,auth:a}=t;t.headers=i=ct.from(i),t.url=Zl(oc(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(b.isFormData(n)){if(Ve.hasStandardBrowserEnv||Ve.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...c]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ve.hasStandardBrowserEnv&&(s&&b.isFunction(s)&&(s=s(t)),s||s!==!1&&Pp(t.url))){const u=o&&r&&Ip.read(r);u&&i.set(o,u)}return t},Lp=typeof XMLHttpRequest<"u",Np=Lp&&function(e){return new Promise(function(n,s){const o=rc(e);let r=o.data;const i=ct.from(o.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=o,c,f,p,g,y;function S(){g&&g(),y&&y(),o.cancelToken&&o.cancelToken.unsubscribe(c),o.signal&&o.signal.removeEventListener("abort",c)}let w=new XMLHttpRequest;w.open(o.method.toUpperCase(),o.url,!0),w.timeout=o.timeout;function k(){if(!w)return;const E=ct.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),U={data:!a||a==="text"||a==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:E,config:e,request:w};sc(function(Y){n(Y),S()},function(Y){s(Y),S()},U),w=null}"onloadend"in w?w.onloadend=k:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(k)},w.onabort=function(){w&&(s(new re("Request aborted",re.ECONNABORTED,e,w)),w=null)},w.onerror=function(){s(new re("Network Error",re.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let P=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const U=o.transitional||ec;o.timeoutErrorMessage&&(P=o.timeoutErrorMessage),s(new re(P,U.clarifyTimeoutError?re.ETIMEDOUT:re.ECONNABORTED,e,w)),w=null},r===void 0&&i.setContentType(null),"setRequestHeader"in w&&b.forEach(i.toJSON(),function(P,U){w.setRequestHeader(U,P)}),b.isUndefined(o.withCredentials)||(w.withCredentials=!!o.withCredentials),a&&a!=="json"&&(w.responseType=o.responseType),u&&([p,y]=Hs(u,!0),w.addEventListener("progress",p)),l&&w.upload&&([f,g]=Hs(l),w.upload.addEventListener("progress",f),w.upload.addEventListener("loadend",g)),(o.cancelToken||o.signal)&&(c=E=>{w&&(s(!E||E.type?new An(null,e,w):E),w.abort(),w=null)},o.cancelToken&&o.cancelToken.subscribe(c),o.signal&&(o.signal.aborted?c():o.signal.addEventListener("abort",c)));const T=Rp(o.url);if(T&&Ve.protocols.indexOf(T)===-1){s(new re("Unsupported protocol "+T+":",re.ERR_BAD_REQUEST,e));return}w.send(r||null)})},Dp=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,o;const r=function(u){if(!o){o=!0,a();const c=u instanceof Error?u:this.reason;s.abort(c instanceof re?c:new An(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,r(new re(`timeout ${t} of ms exceeded`,re.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(r):u.removeEventListener("abort",r)}),e=null)};e.forEach(u=>u.addEventListener("abort",r));const{signal:l}=s;return l.unsubscribe=()=>b.asap(a),l}},Mp=Dp,Hp=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let s=0,o;for(;s<n;)o=s+t,yield e.slice(s,o),s=o},Fp=async function*(e,t){for await(const n of Up(e))yield*Hp(n,t)},Up=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},zi=(e,t,n,s)=>{const o=Fp(e,t);let r=0,i,a=l=>{i||(i=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:u,value:c}=await o.next();if(u){a(),l.close();return}let f=c.byteLength;if(n){let p=r+=f;n(p)}l.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(l){return a(l),o.return()}},{highWaterMark:2})},co=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ic=co&&typeof ReadableStream=="function",Vp=co&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ac=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Bp=ic&&ac(()=>{let e=!1;const t=new Request(Ve.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),qi=64*1024,Zo=ic&&ac(()=>b.isReadableStream(new Response("").body)),Fs={stream:Zo&&(e=>e.body)};co&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Fs[t]&&(Fs[t]=b.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new re(`Response type '${t}' is not supported`,re.ERR_NOT_SUPPORT,s)})})})(new Response);const jp=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(Ve.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await Vp(e)).byteLength},Gp=async(e,t)=>{const n=b.toFiniteNumber(e.getContentLength());return n??jp(t)},$p=co&&(async e=>{let{url:t,method:n,data:s,signal:o,cancelToken:r,timeout:i,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:p}=rc(e);u=u?(u+"").toLowerCase():"text";let g=Mp([o,r&&r.toAbortSignal()],i),y;const S=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let w;try{if(l&&Bp&&n!=="get"&&n!=="head"&&(w=await Gp(c,s))!==0){let U=new Request(t,{method:"POST",body:s,duplex:"half"}),Q;if(b.isFormData(s)&&(Q=U.headers.get("content-type"))&&c.setContentType(Q),U.body){const[Y,q]=$i(w,Hs(Ki(l)));s=zi(U.body,qi,Y,q)}}b.isString(f)||(f=f?"include":"omit");const k="credentials"in Request.prototype;y=new Request(t,{...p,signal:g,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:k?f:void 0});let T=await fetch(y,p);const E=Zo&&(u==="stream"||u==="response");if(Zo&&(a||E&&S)){const U={};["status","statusText","headers"].forEach(x=>{U[x]=T[x]});const Q=b.toFiniteNumber(T.headers.get("content-length")),[Y,q]=a&&$i(Q,Hs(Ki(a),!0))||[];T=new Response(zi(T.body,qi,Y,()=>{q&&q(),S&&S()}),U)}u=u||"text";let P=await Fs[b.findKey(Fs,u)||"text"](T,e);return!E&&S&&S(),await new Promise((U,Q)=>{sc(U,Q,{data:P,headers:ct.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:y})})}catch(k){throw S&&S(),k&&k.name==="TypeError"&&/Load failed|fetch/i.test(k.message)?Object.assign(new re("Network Error",re.ERR_NETWORK,e,y),{cause:k.cause||k}):re.from(k,k&&k.code,e,y)}}),er={http:sp,xhr:Np,fetch:$p};b.forEach(er,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ji=e=>`- ${e}`,Kp=e=>b.isFunction(e)||e===null||e===!1,lc={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let n,s;const o={};for(let r=0;r<t;r++){n=e[r];let i;if(s=n,!Kp(n)&&(s=er[(i=String(n)).toLowerCase()],s===void 0))throw new re(`Unknown adapter '${i}'`);if(s)break;o[i||"#"+r]=s}if(!s){const r=Object.entries(o).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?r.length>1?`since :
`+r.map(Ji).join(`
`):" "+Ji(r[0]):"as no adapter specified";throw new re("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:er};function Io(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new An(null,e)}function Xi(e){return Io(e),e.headers=ct.from(e.headers),e.data=Po.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),lc.getAdapter(e.adapter||Ar.adapter)(e).then(function(s){return Io(e),s.data=Po.call(e,e.transformResponse,s),s.headers=ct.from(s.headers),s},function(s){return nc(s)||(Io(e),s&&s.response&&(s.response.data=Po.call(e,e.transformResponse,s.response),s.response.headers=ct.from(s.response.headers))),Promise.reject(s)})}const cc="1.11.0",uo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{uo[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Yi={};uo.transitional=function(t,n,s){function o(r,i){return"[Axios v"+cc+"] Transitional option '"+r+"'"+i+(s?". "+s:"")}return(r,i,a)=>{if(t===!1)throw new re(o(i," has been removed"+(n?" in "+n:"")),re.ERR_DEPRECATED);return n&&!Yi[i]&&(Yi[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(r,i,a):!0}};uo.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Wp(e,t,n){if(typeof e!="object")throw new re("options must be an object",re.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let o=s.length;for(;o-- >0;){const r=s[o],i=t[r];if(i){const a=e[r],l=a===void 0||i(a,r,e);if(l!==!0)throw new re("option "+r+" must be "+l,re.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new re("Unknown option "+r,re.ERR_BAD_OPTION)}}const _s={assertOptions:Wp,validators:uo},mt=_s.validators;class Us{constructor(t){this.defaults=t||{},this.interceptors={request:new ji,response:new ji}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const r=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?r&&!String(s.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+r):s.stack=r}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=fn(this.defaults,n);const{transitional:s,paramsSerializer:o,headers:r}=n;s!==void 0&&_s.assertOptions(s,{silentJSONParsing:mt.transitional(mt.boolean),forcedJSONParsing:mt.transitional(mt.boolean),clarifyTimeoutError:mt.transitional(mt.boolean)},!1),o!=null&&(b.isFunction(o)?n.paramsSerializer={serialize:o}:_s.assertOptions(o,{encode:mt.function,serialize:mt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),_s.assertOptions(n,{baseUrl:mt.spelling("baseURL"),withXsrfToken:mt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=r&&b.merge(r.common,r[n.method]);r&&b.forEach(["delete","get","head","post","put","patch","common"],y=>{delete r[y]}),n.headers=ct.concat(i,r);const a=[];let l=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(n)===!1||(l=l&&S.synchronous,a.unshift(S.fulfilled,S.rejected))});const u=[];this.interceptors.response.forEach(function(S){u.push(S.fulfilled,S.rejected)});let c,f=0,p;if(!l){const y=[Xi.bind(this),void 0];for(y.unshift(...a),y.push(...u),p=y.length,c=Promise.resolve(n);f<p;)c=c.then(y[f++],y[f++]);return c}p=a.length;let g=n;for(f=0;f<p;){const y=a[f++],S=a[f++];try{g=y(g)}catch(w){S.call(this,w);break}}try{c=Xi.call(this,g)}catch(y){return Promise.reject(y)}for(f=0,p=u.length;f<p;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=fn(this.defaults,t);const n=oc(t.baseURL,t.url,t.allowAbsoluteUrls);return Zl(n,t.params,t.paramsSerializer)}}b.forEach(["delete","get","head","options"],function(t){Us.prototype[t]=function(n,s){return this.request(fn(s||{},{method:t,url:n,data:(s||{}).data}))}});b.forEach(["post","put","patch"],function(t){function n(s){return function(r,i,a){return this.request(fn(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:r,data:i}))}}Us.prototype[t]=n(),Us.prototype[t+"Form"]=n(!0)});const Cs=Us;class Pr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(r){n=r});const s=this;this.promise.then(o=>{if(!s._listeners)return;let r=s._listeners.length;for(;r-- >0;)s._listeners[r](o);s._listeners=null}),this.promise.then=o=>{let r;const i=new Promise(a=>{s.subscribe(a),r=a}).then(o);return i.cancel=function(){s.unsubscribe(r)},i},t(function(r,i,a){s.reason||(s.reason=new An(r,i,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Pr(function(o){t=o}),cancel:t}}}const zp=Pr;function qp(e){return function(n){return e.apply(null,n)}}function Jp(e){return b.isObject(e)&&e.isAxiosError===!0}const tr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(tr).forEach(([e,t])=>{tr[t]=e});const Xp=tr;function uc(e){const t=new Cs(e),n=Bl(Cs.prototype.request,t);return b.extend(n,Cs.prototype,t,{allOwnKeys:!0}),b.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return uc(fn(e,o))},n}const ke=uc(Ar);ke.Axios=Cs;ke.CanceledError=An;ke.CancelToken=zp;ke.isCancel=nc;ke.VERSION=cc;ke.toFormData=ao;ke.AxiosError=re;ke.Cancel=ke.CanceledError;ke.all=function(t){return Promise.all(t)};ke.spread=qp;ke.isAxiosError=Jp;ke.mergeConfig=fn;ke.AxiosHeaders=ct;ke.formToJSON=e=>tc(b.isHTMLForm(e)?new FormData(e):e);ke.getAdapter=lc.getAdapter;ke.HttpStatusCode=Xp;ke.default=ke;const fc=ke,Ft=(e,t="")=>({VITE_API_BASE_URL:"https://api.xiaolidianjing.com/api",VITE_FRONTEND_URL:"https://xiaolidianjing.com",VITE_BACKEND_URL:"https://api.xiaolidianjing.com",VITE_APP_NAME:"连连看游戏",VITE_APP_VERSION:"1.0.0",VITE_NODE_ENV:"production",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1})[e]||t,Rn={api:{baseURL:Ft("VITE_API_BASE_URL","http://localhost:8080/api"),timeout:1e4},domain:{frontend:Ft("VITE_FRONTEND_URL","http://localhost:5173"),backend:Ft("VITE_BACKEND_URL","http://localhost:8080")},app:{name:Ft("VITE_APP_NAME","连连看游戏"),version:Ft("VITE_APP_VERSION","1.0.0"),env:Ft("VITE_NODE_ENV","development")},isDev:Ft("VITE_NODE_ENV","development")==="development",isProd:Ft("VITE_NODE_ENV","development")==="production"};Rn.isDev&&console.log("🔧 应用配置信息:",Rn);const Ke=fc.create({baseURL:Rn.api.baseURL,timeout:Rn.api.timeout});Ke.interceptors.request.use(e=>e,e=>Promise.reject(e));Ke.interceptors.response.use(e=>e.data,e=>(console.error("请求错误:",e),Promise.reject(e)));const Et={createGameSession(e){return Ke.post("/session/create",{playerName:e})},getGameSessionState(e){return Ke.get(`/session/${e}`)},updateGameSessionState(e,t,n,s){return Ke.post("/session/update",{sessionId:e,boardState:t,score:n,isCompleted:s})},getDefaultBoardState(){return Ke.get("/game/board")},getBoardStateByVersion(e){return Ke.get(`/game/board/${e}`)},getBoardVersions(){return Ke.get("/game/versions")},getBoardData(e=1){return Ke.get(`/board/${e}`)},saveGameProgress(e,t){return Ke.post("/game/save",{boardState:e,playerName:t})},loadGameProgress(e){return Ke.post("/game/load",{progressCode:e})},resetGame(){return Ke.post("/game/reset")},exportProgress(e){return Ke.post("/progress/export",{boardState:e})},importProgress(e){return Ke.post("/progress/import",{progressCode:e})}},dc="game_player_name";function xs(e){if(!e||typeof e!="string")return console.warn("无效的玩家名称:",e),!1;try{const t=e.trim();return t.length===0?(console.warn("玩家名称不能为空"),!1):(localStorage.setItem(dc,t),console.log("玩家名称已保存:",t),!0)}catch(t){return console.error("保存玩家名称失败:",t),!1}}function qn(){try{const e=localStorage.getItem(dc);return e?e.trim():null}catch(e){return console.error("获取玩家名称失败:",e),null}}function Yp(){const e=qn();return e!==null&&e.length>0}function hc(e="游客"){return qn()||e}function Qp(e,t,n){if(e.itemId!==t.itemId||e.id===t.id)return null;const s=n.length,o=n[0].length;if(Qi(e)||Qi(t))return Zp(e,t,n,o,s);const r=ea(e),i=ea(t);for(const a of r)for(const l of i){const u=mc(a,l,n,o,s,e,t);if(u)return{path:u,from:e,to:t}}return null}function Qi(e){return e.width>1||e.height>1}function Zp(e,t,n,s,o){const r={x:e.x,y:e.y},i={x:t.x,y:t.y},a=mc(r,i,n,s,o,e,t);return!a||!em(e,a,n,t)?null:{path:a,from:e,to:t}}function em(e,t,n,s){for(let o=0;o<t.length;o++){const r=t[o],i=Zi(e,r);if(!pc(e,i,n,s))return!1;if(o<t.length-1){const a=t[o+1],l=Zi(e,a);if(!tm(e,i,l,n,s))return!1}}return!0}function Zi(e,t){return{x:t.x,y:t.y}}function tm(e,t,n,s,o){const r=n.x-t.x,i=n.y-t.y,a=Math.max(Math.abs(r),Math.abs(i));if(a===0)return!0;for(let l=0;l<=a;l++){const u=l/a,c={x:Math.round(t.x+r*u),y:Math.round(t.y+i*u)};if(!pc(e,c,s,o))return!1}return!0}function pc(e,t,n,s){for(let o=0;o<e.height;o++)for(let r=0;r<e.width;r++){const i=t.x+r,a=t.y+o;if(i<0||i>=n[0].length||a<0||a>=n.length)return!1;const l=n[a][i];if(l&&!l.isRemoved&&l.id!==e.id&&l.id!==s.id)return!1}return!0}function ea(e){const t=[];for(let n=0;n<e.height;n++)for(let s=0;s<e.width;s++)t.push({x:e.x+s,y:e.y+n});return t}function mc(e,t,n,s,o,r,i){let a=it(e,t,n,s,o,r,i);return a||(a=nm(e,t,n,s,o,r,i),a)||(a=sm(e,t,n,s,o,r,i),a)?a:null}function it(e,t,n,s,o,r,i){if(e.y===t.y){const a=Math.min(e.x,t.x),l=Math.max(e.x,t.x);for(let u=a;u<=l;u++)if(!zt(u,e.y,r,n,i))return null;return[e,t]}if(e.x===t.x){const a=Math.min(e.y,t.y),l=Math.max(e.y,t.y);for(let u=a;u<=l;u++)if(!zt(e.x,u,r,n,i))return null;return[e,t]}return null}function nm(e,t,n,s,o,r,i){const a={x:e.x,y:t.y};if(zt(a.x,a.y,r,n,i)){const u=it(e,a,n,s,o,r,i),c=it(a,t,n,s,o,r,i);if(u&&c)return[e,a,t]}const l={x:t.x,y:e.y};if(zt(l.x,l.y,r,n,i)){const u=it(e,l,n,s,o,r,i),c=it(l,t,n,s,o,r,i);if(u&&c)return[e,l,t]}return null}function sm(e,t,n,s,o,r,i){for(let a=0;a<s;a++){if(a===e.x)continue;const l={x:a,y:e.y},u={x:a,y:t.y};if(zt(l.x,l.y,r,n,i)&&zt(u.x,u.y,r,n,i)){const c=it(e,l,n,s,o,r,i),f=it(l,u,n,s,o,r,i),p=it(u,t,n,s,o,r,i);if(c&&f&&p)return[e,l,u,t]}}for(let a=0;a<o;a++){if(a===e.y)continue;const l={x:e.x,y:a},u={x:t.x,y:a};if(zt(l.x,l.y,r,n,i)&&zt(u.x,u.y,r,n,i)){const c=it(e,l,n,s,o,r,i),f=it(l,u,n,s,o,r,i),p=it(u,t,n,s,o,r,i);if(c&&f&&p)return[e,l,u,t]}}return null}function zt(e,t,n,s,o){for(let r=0;r<n.height;r++)for(let i=0;i<n.width;i++){const a=e+i,l=t+r;if(a<0||a>=s[0].length||l<0||l>=s.length)return!1;const u=s[l][a];if(u&&!u.isRemoved&&u.id!==n.id&&u.id!==o.id)return!1}return!0}const gc=Tl("game",{state:()=>({sessionId:null,playerName:hc(""),boardState:null,score:0,isCompleted:!1,availableVersions:[],currentVersion:"simple",isLoading:!1,selectedItems:[],connectionLine:null,removingItems:[],startTime:null,gameTime:0,gameHistory:[],maxHistorySize:15,canUndo:!1,storedRed:{red6:0,red9:0,red12:0},pendingDatabaseUpdate:!1,isSharing:!1}),getters:{boardGrid(){var t;if(!this.boardState)return[];const e=Array(this.boardState.height).fill(null).map(()=>Array(this.boardState.width).fill(null));return(t=this.boardState.items)==null||t.forEach(n=>{if(!n.isRemoved)for(let s=0;s<n.height;s++)for(let o=0;o<n.width;o++)n.y+s<this.boardState.height&&n.x+o<this.boardState.width&&(e[n.y+s][n.x+o]=n)}),e},activeItems(){var e,t;return((t=(e=this.boardState)==null?void 0:e.items)==null?void 0:t.filter(n=>!n.isRemoved))||[]},isGameCompleted(){return this.activeItems.length===0},undoStepsCount(){return this.gameHistory.length},lastMoveDescription(){return this.gameHistory.length===0?"":this.gameHistory[this.gameHistory.length-1].description||""}},actions:{async loadAvailableVersions(){try{const e=await Et.getBoardVersions();e.code===200&&(this.availableVersions=e.data)}catch{}},async switchVersion(e){try{this.isLoading=!0;const t=await Et.getBoardStateByVersion(e);if(t.code===200)return this.currentVersion=e,this.boardState=t.data,this.selectedItems=[],this.score=0,this.isCompleted=!1,this.clearUndoHistory(),this.clearStoredRed(),this.pendingDatabaseUpdate=!1,!0;throw new Error(t.message)}catch(t){throw t}finally{this.isLoading=!1}},async createNewGame(e){try{this.isLoading=!0,this.playerName=e,xs(e);const t=await Et.createGameSession(e);if(t.code===200)return this.sessionId=t.data.sessionId,this.boardState=t.data.boardState,this.startTime=new Date(t.data.startTime),this.score=0,this.isCompleted=!1,this.selectedItems=[],this.clearUndoHistory(),this.clearStoredRed(),this.pendingDatabaseUpdate=!1,!0;throw new Error(t.message)}catch(t){throw console.error("创建游戏失败:",t),t}finally{this.isLoading=!1}},async loadGameSession(e){try{this.isLoading=!0;const t=await Et.getGameSessionState(e);if(t.code===200){this.sessionId=t.data.sessionId,this.playerName=t.data.playerName,this.boardState=t.data.boardState,this.score=t.data.score,this.isCompleted=t.data.isCompleted,this.startTime=new Date(t.data.startTime),this.selectedItems=[];const n=t.data.boardState;return n.width===6&&n.height===6?this.currentVersion="simple":n.width===15&&n.height===15?this.currentVersion="hard":this.currentVersion="normal",t.data.boardState.storedRed?this.storedRed=t.data.boardState.storedRed:this.clearStoredRed(),!0}else throw new Error(t.message)}catch(t){throw console.error("加载游戏会话失败:",t),t}finally{this.isLoading=!1}},async updateGameState(){if(this.sessionId)try{const e={...this.boardState,storedRed:this.storedRed};await Et.updateGameSessionState(this.sessionId,e,this.score,this.isCompleted),this.pendingDatabaseUpdate=!1}catch(e){console.error("更新游戏状态失败:",e)}},saveGameState(e=""){const t={boardState:JSON.parse(JSON.stringify(this.boardState)),score:this.score,selectedItems:[...this.selectedItems],storedRed:JSON.parse(JSON.stringify(this.storedRed)),timestamp:Date.now(),description:e};this.gameHistory.push(t),this.gameHistory.length>this.maxHistorySize&&this.gameHistory.shift(),this.canUndo=this.gameHistory.length>0},undoLastMove(){if(this.gameHistory.length===0)return console.warn("没有可撤销的操作"),!1;const e=this.gameHistory.pop();return this.boardState=e.boardState,this.score=e.score,this.selectedItems=e.selectedItems,e.storedRed&&(this.storedRed=e.storedRed),this.connectionLine=null,this.removingItems=[],this.canUndo=this.gameHistory.length>0,this.pendingDatabaseUpdate=!0,!0},selectItem(e){this.selectedItems.length>=2&&(this.selectedItems=[]);const t=this.selectedItems.findIndex(n=>n.id===e.id);t>=0?this.selectedItems.splice(t,1):(this.selectedItems.push(e),this.selectedItems.length===2&&this.checkConnection())},checkConnection(){if(this.selectedItems.length!==2)return;const[e,t]=this.selectedItems,n=Qp(e,t,this.boardGrid);if(n){const s=`消除了${e.name||"物品"}和${t.name||"物品"}`;this.saveGameState(s),this.showConnectionAnimation(n.path),setTimeout(()=>{this.showRemoveAnimation([e,t]),setTimeout(()=>{this.removeItems([e,t]),this.score+=this.calculateScore(e,t),this.pendingDatabaseUpdate=!0,this.isGameCompleted&&(this.isCompleted=!0,this.clearUndoHistory(),this.updateGameState()),this.clearSelection()},800)},800)}else this.showConnectionFailed(),this.clearSelection()},isLargeItem(e){return e.width>1||e.height>1},calculateScore(e,t){let n=10;return(this.isLargeItem(e)||this.isLargeItem(t))&&(n+=20),n},showConnectionAnimation(e){this.connectionLine={path:e,timestamp:Date.now()},this.forceRefreshCallback&&setTimeout(()=>{this.forceRefreshCallback()},10),setTimeout(()=>{this.connectionLine=null},800)},setForceRefreshCallback(e){this.forceRefreshCallback=e},showRemoveAnimation(e){this.removingItems=[...e],setTimeout(()=>{this.removingItems=[]},800)},showConnectionFailed(){},clearSelection(){setTimeout(()=>{this.selectedItems=[]},500)},removeItems(e){e.forEach(t=>{const n=this.boardState.items.find(s=>s.id===t.id);n&&(n.isRemoved=!0)})},async resetGame(){if(this.sessionId)try{const e=await Et.getBoardStateByVersion(this.currentVersion);e.code===200&&(this.boardState=e.data,this.score=0,this.isCompleted=!1,this.selectedItems=[],this.clearUndoHistory(),this.clearStoredRed(),this.updateGameState())}catch(e){console.error("重置游戏失败:",e)}},async exportGameProgress(){if(!this.sessionId||!this.boardState)throw new Error("没有可导出的游戏数据");try{const e={...this.boardState,storedRed:this.storedRed},t=await Et.exportProgress(e);if(t.code===200)return t.data.progressCode;throw new Error(t.message)}catch(e){throw console.error("导出游戏进度失败:",e),e}},async importGameProgress(e){try{const t=await Et.importProgress(e);if(t.code===200){this.boardState=t.data.boardState,this.selectedItems=[],this.connectionLine=null,this.removingItems=[];const n=t.data.boardState;return n.width===6&&n.height===6?this.currentVersion="simple":n.width===15&&n.height===15?this.currentVersion="hard":this.currentVersion="normal",t.data.boardState.storedRed?this.storedRed=t.data.boardState.storedRed:this.clearStoredRed(),this.clearUndoHistory(),this.sessionId&&await this.updateGameState(),!0}else throw new Error(t.message)}catch(t){throw console.error("导入游戏进度失败:",t),t}},async saveCurrentProgress(){if(!this.sessionId||!this.boardState)throw new Error("没有可保存的游戏数据");try{const e={...this.boardState,storedRed:this.storedRed},t=await Et.saveGameProgress(e,this.playerName);if(t.code===200)return await this.updateGameState(),t.data.progressCode;throw new Error(t.message)}catch(e){throw console.error("保存游戏进度失败:",e),e}},clearUndoHistory(){this.gameHistory=[],this.canUndo=!1},increaseStoredRed(e){this.storedRed.hasOwnProperty(e)&&(this.storedRed[e]++,this.pendingDatabaseUpdate=!0)},decreaseStoredRed(e){this.storedRed.hasOwnProperty(e)&&this.storedRed[e]>0&&(this.storedRed[e]--,this.pendingDatabaseUpdate=!0)},setStoredRed(e,t){this.storedRed.hasOwnProperty(e)&&t>=0&&(this.storedRed[e]=t,this.pendingDatabaseUpdate=!0)},clearStoredRed(){this.storedRed={red6:0,red9:0,red12:0},this.pendingDatabaseUpdate=!0},clearGame(){this.sessionId=null,this.playerName="",this.boardState=null,this.score=0,this.isCompleted=!1,this.selectedItems=[],this.connectionLine=null,this.removingItems=[],this.startTime=null,this.gameTime=0,this.clearUndoHistory(),this.isSharing=!1,this.pendingDatabaseUpdate=!1},async shareGameProgress(){if(this.isSharing)return!1;try{this.isSharing=!0;const e=await this.exportGameProgress(),{shareGameProgress:t}=await Ms(()=>import("./shareUtils-9f678a7f.js"),[]);return await t(e)}catch(e){console.error("分享失败:",e);const{toast:t}=await Ms(()=>Promise.resolve().then(()=>zy),void 0);return t.error("分享失败: "+e.message),!1}finally{this.isSharing=!1}}}}),vt=fc.create({baseURL:Rn.api.baseURL,timeout:Rn.api.timeout});vt.interceptors.request.use(e=>e,e=>Promise.reject(e));vt.interceptors.response.use(e=>e.data,e=>(console.error("请求错误:",e),Promise.reject(e)));const Ye={createGameSession(e,t="normal"){return vt.post("/item-selection/create",{playerName:e,difficultyLevel:t})},getGameState(e){return vt.get(`/item-selection/${e}`)},updateGameState(e,t){return vt.post("/item-selection/update",{sessionId:e,items:t})},confirmSelection(e){return vt.post("/item-selection/confirm",{sessionId:e})},resetGame(e){return vt.post("/item-selection/reset",{sessionId:e})},exportGameState(e,t,n){return vt.post("/item-selection/export",{sessionId:e,items:t,playerName:n})},importGameState(e,t){return vt.post("/item-selection/import",{sessionId:e,importCode:t})},getDifficultyLevels(){return vt.get("/item-selection/difficulty-levels")}};const om={name:"Home",setup(){const e=Cr(),t=gc(),n=oe(!1),s=oe(!1),o=oe(!1),r=oe(!1),i=oe(!1),a=oe(""),l=oe(""),u=oe(""),c=oe(""),f=oe(!1);dn(async()=>{const x=qn();x&&(a.value=x,u.value=x,c.value=x),q()});const p=()=>{i.value=!1,c.value=qn()||""},g=()=>{if(!c.value.trim())return;const x=c.value.trim();xs(x),a.value=x,u.value=x,p()},y=()=>{n.value=!0},S=()=>{n.value=!1,s.value=!0},w=()=>{n.value=!1},k=()=>{e.push("/wheel")},T=()=>{r.value=!0},E=()=>{r.value=!1,u.value=""},P=async()=>{if(u.value.trim())try{f.value=!0;const x=u.value.trim();xs(x);const z=await Ye.createGameSession(x,"normal");e.push(`/item-selection/${z.data.sessionId}`)}catch(x){alert("创建游戏失败: "+x.message)}finally{f.value=!1,E()}},U=async()=>{if(a.value.trim())try{f.value=!0;const x=a.value.trim();xs(x),await t.createNewGame(x),e.push(`/game/${t.sessionId}`)}catch(x){alert("创建游戏失败: "+x.message)}finally{f.value=!1,Y()}},Q=async()=>{if(l.value.trim())try{f.value=!0,await t.createNewGame("临时玩家"),await t.importGameProgress(l.value.trim()),e.push(`/game/${t.sessionId}`)}catch(x){alert("加载游戏失败: "+x.message)}finally{f.value=!1,Y()}},Y=()=>{s.value=!1,o.value=!1,a.value="",l.value=""},q=async()=>{const x=new URLSearchParams(window.location.search),z=x.get("share");if(z){x.delete("share"),window.history.replaceState({},"",window.location.pathname+(x.toString()?"?"+x.toString():""));try{f.value=!0,await t.createNewGame("分享玩家"),await t.importGameProgress(decodeURIComponent(z)),e.push(`/game/${t.sessionId}`)}catch(L){alert("加载分享的连连看游戏失败: "+L.message)}finally{f.value=!1}return}const ne=x.get("itemShare");if(ne){x.delete("itemShare"),window.history.replaceState({},"",window.location.pathname+(x.toString()?"?"+x.toString():""));try{f.value=!0;const L=await Ye.createGameSession("分享玩家","normal");await Ye.importGameState(L.data.sessionId,decodeURIComponent(ne)),e.push(`/item-selection/${L.data.sessionId}`)}catch(L){alert("加载分享的物品选择游戏失败: "+L.message)}finally{f.value=!1}}};return{showLianLianKanDialog:n,showNameDialog:s,showLoadDialog:o,showItemSelectionDialog:r,showChangeNameDialog:i,playerName:a,progressCode:l,itemSelectionPlayerName:u,newPlayerName:c,isLoading:f,startLianLianKan:y,startNewLianLianKan:S,closeLianLianKanDialog:w,createGame:U,loadGame:Q,closeDialog:Y,closeChangeNameDialog:p,changePlayerName:g,goToWheelGame:k,startItemSelectionGame:T,closeItemSelectionDialog:E,createItemSelectionGame:P,getPlayerName:qn,hasPlayerName:Yp}}},rm={class:"home"},im={class:"container"},am={class:"game-cards"},lm={class:"game-options"},cm={class:"dialog-buttons"},um={class:"dialog-buttons"},fm=["disabled"],dm={class:"dialog-buttons"},hm=["disabled"],pm={class:"dialog-buttons"},mm=["disabled"],gm={class:"dialog-buttons"},ym=["disabled"],vm={key:5,class:"loading"};function bm(e,t,n,s,o,r){return j(),G("div",rm,[d("div",im,[d("div",am,[d("div",{class:"game-card",onClick:t[0]||(t[0]=(...i)=>s.startLianLianKan&&s.startLianLianKan(...i))},t[32]||(t[32]=[Dn('<div class="card-header" data-v-7f4d3eb3><div class="card-status available" data-v-7f4d3eb3>可玩</div></div><div class="card-content" data-v-7f4d3eb3><h3 data-v-7f4d3eb3>连连看</h3><p data-v-7f4d3eb3>经典的连连看消除游戏，考验你的观察力和策略思维</p></div><div class="card-footer" data-v-7f4d3eb3><button class="play-btn" data-v-7f4d3eb3>开始游戏</button></div>',3)])),d("div",{class:"game-card",onClick:t[1]||(t[1]=(...i)=>s.goToWheelGame&&s.goToWheelGame(...i))},t[33]||(t[33]=[Dn('<div class="card-header" data-v-7f4d3eb3><div class="card-status available" data-v-7f4d3eb3>可玩</div></div><div class="card-content" data-v-7f4d3eb3><h3 data-v-7f4d3eb3> 幸运转盘</h3><p data-v-7f4d3eb3>全新的转盘游戏</p></div><div class="card-footer" data-v-7f4d3eb3><button class="play-btn" data-v-7f4d3eb3>开始游戏</button></div>',3)])),d("div",{class:"game-card",onClick:t[2]||(t[2]=(...i)=>s.startItemSelectionGame&&s.startItemSelectionGame(...i))},t[34]||(t[34]=[Dn('<div class="card-header" data-v-7f4d3eb3><div class="card-status available" data-v-7f4d3eb3>可玩</div></div><div class="card-content" data-v-7f4d3eb3><h3 data-v-7f4d3eb3>Bingo玩法</h3><p data-v-7f4d3eb3>Bingo玩法普通版+困难版</p></div><div class="card-footer" data-v-7f4d3eb3><button class="play-btn" data-v-7f4d3eb3>开始游戏</button></div>',3)])),t[35]||(t[35]=Dn('<div class="game-card coming-soon" data-v-7f4d3eb3><div class="card-header" data-v-7f4d3eb3><div class="card-status coming-soon" data-v-7f4d3eb3>即将推出</div></div><div class="card-content" data-v-7f4d3eb3><h3 data-v-7f4d3eb3>敬请期待</h3><p data-v-7f4d3eb3>敬请期待</p></div><div class="card-footer" data-v-7f4d3eb3><button class="play-btn" disabled data-v-7f4d3eb3>敬请期待</button></div></div>',1))]),s.showLianLianKanDialog?(j(),G("div",{key:0,class:"dialog-overlay",onClick:t[7]||(t[7]=(...i)=>s.closeLianLianKanDialog&&s.closeLianLianKanDialog(...i))},[d("div",{class:"dialog game-options-dialog",onClick:t[6]||(t[6]=Kt(()=>{},["stop"]))},[t[38]||(t[38]=d("h3",null,"连连看游戏",-1)),t[39]||(t[39]=d("p",{class:"dialog-subtitle"},"选择游戏模式",-1)),d("div",lm,[d("div",{class:"option-card",onClick:t[3]||(t[3]=(...i)=>s.startNewLianLianKan&&s.startNewLianLianKan(...i))},t[36]||(t[36]=[d("div",{class:"option-icon"},"🆕",-1),d("h4",null,"开始新游戏",-1),d("p",null,"开始一局全新的连连看游戏",-1)])),d("div",{class:"option-card",onClick:t[4]||(t[4]=i=>s.showLoadDialog=!0)},t[37]||(t[37]=[d("div",{class:"option-icon"},"📂",-1),d("h4",null,"加载游戏",-1),d("p",null,"输入进度代码继续之前的游戏",-1)]))]),d("div",cm,[d("button",{onClick:t[5]||(t[5]=(...i)=>s.closeLianLianKanDialog&&s.closeLianLianKanDialog(...i)),class:"btn-cancel"},"取消")])])])):xe("",!0),s.showItemSelectionDialog?(j(),G("div",{key:1,class:"dialog-overlay",onClick:t[13]||(t[13]=(...i)=>s.closeItemSelectionDialog&&s.closeItemSelectionDialog(...i))},[d("div",{class:"dialog",onClick:t[12]||(t[12]=Kt(()=>{},["stop"]))},[t[40]||(t[40]=d("h3",null,"物品选择游戏",-1)),Wt(d("input",{"onUpdate:modelValue":t[8]||(t[8]=i=>s.itemSelectionPlayerName=i),type:"text",placeholder:"请输入您的名称",onKeyup:t[9]||(t[9]=Mn((...i)=>s.createItemSelectionGame&&s.createItemSelectionGame(...i),["enter"])),maxlength:"20"},null,544),[[rn,s.itemSelectionPlayerName]]),d("div",um,[d("button",{onClick:t[10]||(t[10]=(...i)=>s.closeItemSelectionDialog&&s.closeItemSelectionDialog(...i)),class:"btn-cancel"},"取消"),d("button",{onClick:t[11]||(t[11]=(...i)=>s.createItemSelectionGame&&s.createItemSelectionGame(...i)),class:"btn-confirm",disabled:!s.itemSelectionPlayerName.trim()}," 开始游戏 ",8,fm)])])])):xe("",!0),s.showNameDialog?(j(),G("div",{key:2,class:"dialog-overlay",onClick:t[19]||(t[19]=(...i)=>s.closeDialog&&s.closeDialog(...i))},[d("div",{class:"dialog",onClick:t[18]||(t[18]=Kt(()=>{},["stop"]))},[t[41]||(t[41]=d("h3",null,"输入玩家名称",-1)),Wt(d("input",{"onUpdate:modelValue":t[14]||(t[14]=i=>s.playerName=i),type:"text",placeholder:"请输入您的名称",onKeyup:t[15]||(t[15]=Mn((...i)=>s.createGame&&s.createGame(...i),["enter"])),maxlength:"20"},null,544),[[rn,s.playerName]]),d("div",dm,[d("button",{onClick:t[16]||(t[16]=(...i)=>s.closeDialog&&s.closeDialog(...i)),class:"btn-cancel"},"取消"),d("button",{onClick:t[17]||(t[17]=(...i)=>s.createGame&&s.createGame(...i)),class:"btn-confirm",disabled:!s.playerName.trim()}," 开始游戏 ",8,hm)])])])):xe("",!0),s.showLoadDialog?(j(),G("div",{key:3,class:"dialog-overlay",onClick:t[25]||(t[25]=(...i)=>s.closeDialog&&s.closeDialog(...i))},[d("div",{class:"dialog",onClick:t[24]||(t[24]=Kt(()=>{},["stop"]))},[t[42]||(t[42]=d("h3",null,"加载连连看游戏",-1)),Wt(d("input",{"onUpdate:modelValue":t[20]||(t[20]=i=>s.progressCode=i),type:"text",placeholder:"请输入进度代码",onKeyup:t[21]||(t[21]=Mn((...i)=>s.loadGame&&s.loadGame(...i),["enter"])),maxlength:"50"},null,544),[[rn,s.progressCode]]),d("div",pm,[d("button",{onClick:t[22]||(t[22]=(...i)=>s.closeDialog&&s.closeDialog(...i)),class:"btn-cancel"},"取消"),d("button",{onClick:t[23]||(t[23]=(...i)=>s.loadGame&&s.loadGame(...i)),class:"btn-confirm",disabled:!s.progressCode.trim()}," 加载游戏 ",8,mm)])])])):xe("",!0),s.showChangeNameDialog?(j(),G("div",{key:4,class:"dialog-overlay",onClick:t[31]||(t[31]=(...i)=>s.closeChangeNameDialog&&s.closeChangeNameDialog(...i))},[d("div",{class:"dialog",onClick:t[30]||(t[30]=Kt(()=>{},["stop"]))},[d("h3",null,se(s.hasPlayerName()?"修改玩家名称":"设置玩家名称"),1),Wt(d("input",{"onUpdate:modelValue":t[26]||(t[26]=i=>s.newPlayerName=i),type:"text",placeholder:"请输入您的名称",onKeyup:t[27]||(t[27]=Mn((...i)=>s.changePlayerName&&s.changePlayerName(...i),["enter"])),maxlength:"20"},null,544),[[rn,s.newPlayerName]]),d("div",gm,[d("button",{onClick:t[28]||(t[28]=(...i)=>s.closeChangeNameDialog&&s.closeChangeNameDialog(...i)),class:"btn-cancel"},"取消"),d("button",{onClick:t[29]||(t[29]=(...i)=>s.changePlayerName&&s.changePlayerName(...i)),class:"btn-confirm",disabled:!s.newPlayerName.trim()}," 确定 ",8,ym)])])])):xe("",!0),s.isLoading?(j(),G("div",vm,t[43]||(t[43]=[d("div",{class:"spinner"},null,-1),d("p",null,"正在创建游戏...",-1)]))):xe("",!0)])])}const Sm=Nt(om,[["render",bm],["__scopeId","data-v-7f4d3eb3"]]);function yc(){return navigator&&navigator.clipboard&&typeof navigator.clipboard.writeText=="function"}function Ir(){return window.isSecureContext||location.protocol==="https:"||location.hostname==="localhost"}function ta(e){try{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.top="0",t.style.left="0",t.style.width="2em",t.style.height="2em",t.style.padding="0",t.style.border="none",t.style.outline="none",t.style.boxShadow="none",t.style.background="transparent",t.style.opacity="0",document.body.appendChild(t),t.focus(),t.select();const n=document.execCommand("copy");return document.body.removeChild(t),n}catch(t){return console.error("传统复制方法失败:",t),!1}}async function vc(e){if(!e)return console.warn("复制的文本不能为空"),!1;if(yc()&&Ir())try{return await navigator.clipboard.writeText(e),!0}catch(t){return console.warn("现代剪贴板API失败，尝试传统方法:",t),ta(e)}return ta(e)}function bc(e,t="已复制到剪贴板",n="复制失败，请手动复制"){alert(e?t:n)}async function Sc(e,t="代码已复制到剪贴板",n="复制失败，请手动复制代码"){const s=await vc(e);return bc(s,t,n),s}function wm(){return{hasNavigator:typeof navigator<"u",hasClipboard:typeof navigator<"u"&&"clipboard"in navigator,hasWriteText:typeof navigator<"u"&&navigator.clipboard&&typeof navigator.clipboard.writeText=="function",isSecureContext:Ir(),protocol:location.protocol,hostname:location.hostname}}const _m=Object.freeze(Object.defineProperty({__proto__:null,copyTextWithFeedback:Sc,copyToClipboard:vc,getClipboardInfo:wm,isClipboardSupported:yc,isSecureContext:Ir,showCopyResult:bc},Symbol.toStringTag,{value:"Module"}));const Cm={name:"ConnectionLine",props:{connectionLine:{type:Object,default:null},cellSize:{type:Number,default:60},boardOffset:{type:Object,default:()=>({x:0,y:0})},boardPadding:{type:Number,default:32}},setup(e){const t=Ee(()=>window.innerWidth),n=Ee(()=>window.innerHeight),s=oe(0),o=Ee(()=>{var f,p,g;if(!((f=e.connectionLine)!=null&&f.path))return[];s.value;const i=((p=e.boardOffset)==null?void 0:p.x)||0,a=((g=e.boardOffset)==null?void 0:g.y)||0,l=e.boardPadding||32,u=e.cellSize||60;return e.connectionLine.path.map(y=>({x:i+l+(y.x+.5)*u,y:a+l+(y.y+.5)*u}))});St(()=>e.connectionLine,async i=>{i&&(s.value++,await Pt(),setTimeout(()=>{s.value++},50))},{immediate:!0});const r=Ee(()=>o.value.map(i=>`${i.x},${i.y}`).join(" "));return{svgWidth:t,svgHeight:n,pathPointsArray:o,pathPoints:r,recalcTrigger:s}}},xm={key:0,class:"connection-overlay"},Em=["width","height"],Rm=["points"],Tm=["cx","cy"];function Am(e,t,n,s,o,r){return n.connectionLine?(j(),G("div",xm,[(j(),G("svg",{class:"connection-svg",width:s.svgWidth,height:s.svgHeight},[d("polyline",{points:s.pathPoints,class:"connection-path",fill:"none",stroke:"#ff9800","stroke-width":"3","stroke-dasharray":"5,5"},t[0]||(t[0]=[d("animate",{attributeName:"stroke-dashoffset",values:"0;10",dur:"0.5s",repeatCount:"indefinite"},null,-1)]),8,Rm),(j(!0),G(Ce,null,nt(s.pathPointsArray,(i,a)=>(j(),G("circle",{key:a,cx:i.x,cy:i.y,r:"4",fill:"#ff9800",class:"path-point"},t[1]||(t[1]=[d("animate",{attributeName:"r",values:"4;8;4",dur:"0.8s",begin:"0s"},null,-1)]),8,Tm))),128))],8,Em))])):xe("",!0)}const Pm=Nt(Cm,[["render",Am],["__scopeId","data-v-b8baa436"]]);const Im={name:"RemoveAnimation",props:{removingItems:{type:Array,default:()=>[]},cellSize:{type:Number,default:60},boardOffset:{type:Object,default:()=>({x:0,y:0})},boardPadding:{type:Number,default:32}},setup(e){return{getItemPosition:o=>{const r=e.boardOffset.x+e.boardPadding+o.x*e.cellSize,i=e.boardOffset.y+e.boardPadding+o.y*e.cellSize;return{position:"absolute",left:`${r}px`,top:`${i}px`,width:`${o.width*e.cellSize}px`,height:`${o.height*e.cellSize}px`,zIndex:1e3}},getParticleStyle:o=>{const r=o*45*Math.PI/180,i=30,a=Math.cos(r)*i,l=Math.sin(r)*i;return{"--end-x":`${a}px`,"--end-y":`${l}px`,animationDelay:`${o*.05}s`}},getItemImage:o=>`/images/${o.name}.png`}}},Om={key:0,class:"remove-animation-overlay"},km={class:"explosion-effect"},Lm={class:"item-fade"},Nm=["src","alt"];function Dm(e,t,n,s,o,r){return n.removingItems.length>0?(j(),G("div",Om,[(j(!0),G(Ce,null,nt(n.removingItems,i=>(j(),G("div",{key:i.id,class:"removing-item",style:_t(s.getItemPosition(i))},[d("div",km,[(j(),G(Ce,null,nt(8,a=>d("div",{class:"particle",key:a,style:_t(s.getParticleStyle(a))},null,4)),64))]),d("div",Lm,[d("img",{src:s.getItemImage(i),alt:i.name,class:"item-image"},null,8,Nm)])],4))),128))])):xe("",!0)}const Mm=Nt(Im,[["render",Dm],["__scopeId","data-v-78780699"]]);const Hm={name:"Game",components:{ConnectionLine:Pm,RemoveAnimation:Mm},props:{sessionId:String},setup(e){const t=Vl(),n=Cr(),s=gc(),o=oe(!1),r=oe(!1),i=oe(""),a=oe(""),l=oe(null),u=oe(null),c=oe(window.innerWidth),f=oe(window.innerHeight),p=oe(0),g=Ee(()=>{if(p.value,!u.value)return{x:0,y:0};const V=u.value.getBoundingClientRect();return{x:V.left,y:V.top}}),y=Ee(()=>{var Ae,I;const V=((Ae=s.boardState)==null?void 0:Ae.width)||10,le=((I=s.boardState)==null?void 0:I.height)||9;if(c.value<=768){const $=c.value-40,me=Math.floor($/V)-2;return V>=15||le>=15?Math.max(Math.min(35,me),25):V>=10||le>=9?Math.max(Math.min(50,me),35):Math.max(Math.min(60,me),40)}else{let $;c.value<=1600?$=c.value-100:$=c.value-650;const me=Math.floor($/V)-4;return V>=15||le>=15?Math.max(Math.min(55,me),35):V>=10||le>=9?Math.max(Math.min(70,me),50):Math.max(Math.min(80,me),60)}}),S=Ee(()=>c.value<=768?16:32),w=()=>{c.value=window.innerWidth,f.value=window.innerHeight,p.value++},k=()=>{p.value++},T=V=>{V.ctrlKey&&V.key==="z"&&s.canUndo&&(V.preventDefault(),ne()),V.ctrlKey&&V.key==="s"&&(V.preventDefault(),ae())};St(()=>s.boardState,async()=>{s.boardState&&(await Pt(),p.value++)},{deep:!0}),St(()=>s.currentVersion,async()=>{await Pt(),setTimeout(()=>{p.value++},100)}),St(()=>y.value,async()=>{await Pt(),setTimeout(()=>{p.value++},50)}),St(()=>S.value,async()=>{await Pt(),setTimeout(()=>{p.value++},50)}),dn(async()=>{await s.loadAvailableVersions();const V=e.sessionId||t.params.sessionId;if(V&&V!=="undefined")try{await s.loadGameSession(V)}catch(le){console.error("加载游戏会话失败:",le),alert("游戏会话不存在或已过期，将返回首页"),n.push("/")}else try{await s.switchVersion("simple")}catch(le){console.error("加载默认版本失败:",le),n.push("/")}document.addEventListener("keydown",T),window.addEventListener("resize",w),window.addEventListener("scroll",k),await Pt(),p.value++,s.setForceRefreshCallback(()=>{p.value++})}),Xs(()=>{document.removeEventListener("keydown",T),window.removeEventListener("resize",w),window.removeEventListener("scroll",k)});const E=(V,le,Ae)=>{!V||V.isRemoved||(V.width>1||V.height>1||V.x===le&&V.y===Ae)&&s.selectItem(V)},P=V=>V?s.selectedItems.some(le=>le.id===V.id):!1,U=V=>`/images/${V.name}.png`,Q=V=>V.width===1&&V.height===1?{}:{width:`${V.width*y.value-2}px`,height:`${V.height*y.value-2}px`,position:"absolute",top:"1px",left:"1px",zIndex:10},Y=V=>{const le=Math.max(y.value-10,20);return V.width===1&&V.height===1?{width:`${le}px`,height:`${le}px`}:{width:`${V.width*y.value-12}px`,height:`${V.height*y.value-12}px`,position:"absolute",top:"1px",left:"1px",zIndex:10}},q=async V=>{if(V!==s.currentVersion)try{await s.switchVersion(V)}catch(le){console.error("切换版本失败:",le),alert("切换版本失败: "+le.message)}},x=Ee(()=>s.availableVersions?[...s.availableVersions].sort((V,le)=>{const Ae={simple:1,normal:2,hard:3};return(Ae[V.version]||999)-(Ae[le.version]||999)}):[]),z=V=>({simple:"🟢",normal:"🟡",hard:"🔴"})[V]||"⚪",ne=()=>{s.undoLastMove()},L=async()=>{confirm("确定要重置游戏吗？")&&await s.resetGame()},ie=async()=>{try{const V=await s.exportGameProgress();a.value=V,r.value=!0}catch(V){alert("保存失败: "+V.message)}},we=async()=>{if(!i.value.trim()){alert("请输入进度代码");return}try{await s.importGameProgress(i.value.trim()),alert("导入成功!"),fe()}catch(V){alert("导入失败: "+V.message)}},Le=async()=>{await Sc(a.value,"代码已复制到剪贴板！","复制失败，请手动复制代码")},fe=()=>{o.value=!1,i.value=""},Z=()=>{s.clearGame(),n.push("/")},ae=async()=>{await s.shareGameProgress()};return{gameStore:s,showImportDialog:o,showSaveSuccess:r,importCode:i,savedCode:a,boardContainer:l,gameBoard:u,boardOffset:g,cellSize:y,boardPadding:S,selectItem:E,isSelected:P,getItemImage:U,getItemStyle:Q,getItemImageStyle:Y,selectVersion:q,sortedVersions:x,getVersionIcon:z,undoMove:ne,resetGame:L,saveGame:ie,importGame:we,copySaveCode:Le,closeImportDialog:fe,backToHome:Z,shareGame:ae}}},Fm={class:"game-page"},Um={class:"game-header"},Vm={class:"player-info"},Bm={class:"player-name"},jm={class:"game-controls"},Gm={class:"game-main"},$m={class:"game-board-section"},Km={class:"board-announcement-container"},Wm={class:"version-selector-panel"},zm={class:"selector-card"},qm={class:"selector-options"},Jm=["onClick"],Xm={class:"option-icon"},Ym={class:"option-info"},Qm={class:"option-name"},Zm={class:"option-size"},eg={class:"game-board-container",ref:"boardContainer"},tg={key:0,class:"game-board",ref:"gameBoard"},ng=["onClick"],sg={key:0,class:"game-item"},og=["src","alt"],rg={key:1,class:"loading-board"},ig={class:"game-bottom-controls"},ag=["disabled","title"],lg=["disabled"],cg={class:"right-panels"},ug={class:"stored-red-panel"},fg={class:"stored-red-content"},dg={class:"red-item"},hg=["disabled"],pg={class:"red-count"},mg={class:"red-item"},gg=["disabled"],yg={class:"red-count"},vg={class:"red-item"},bg=["disabled"],Sg={class:"red-count"},wg={key:0,class:"game-complete"},_g={class:"complete-dialog"},Cg={class:"complete-stats"},xg={class:"stat-item"},Eg={class:"stat-value"},Rg={class:"stat-item"},Tg={class:"stat-value"},Ag={class:"complete-buttons"},Pg=["disabled"],Ig={class:"dialog-buttons"},Og=["disabled"],kg={key:2,class:"save-success"},Lg={class:"success-content"};function Ng(e,t,n,s,o,r){const i=Is("ConnectionLine"),a=Is("RemoveAnimation");return j(),G(Ce,null,[d("div",Fm,[d("div",Um,[d("div",Vm,[d("span",Bm,"玩家: "+se(s.gameStore.playerName||"游客"),1)]),d("div",jm,[d("button",{onClick:t[0]||(t[0]=(...l)=>s.backToHome&&s.backToHome(...l)),class:"btn-control btn-back"},"返回首页")])]),d("div",Gm,[d("div",$m,[t[32]||(t[32]=d("div",{class:"game-title"},[d("h1",null,"小里连连看")],-1)),d("div",Km,[d("div",Wm,[d("div",zm,[t[23]||(t[23]=d("div",{class:"selector-header"},[d("h3",null,"🎯 选择版本")],-1)),d("div",qm,[(j(!0),G(Ce,null,nt(s.sortedVersions,l=>(j(),G("div",{key:l.version,class:We(["selector-option",{active:s.gameStore.currentVersion===l.version}]),onClick:u=>s.selectVersion(l.version)},[d("div",Xm,se(s.getVersionIcon(l.version)),1),d("div",Ym,[d("div",Qm,se(l.name),1),d("div",Zm,se(l.size),1)])],10,Jm))),128))])])]),d("div",null,[d("div",eg,[s.gameStore.boardState?(j(),G("div",tg,[(j(!0),G(Ce,null,nt(s.gameStore.boardGrid,(l,u)=>(j(),G("div",{key:u,class:"board-row"},[(j(!0),G(Ce,null,nt(l,(c,f)=>(j(),G("div",{key:f,class:We(["board-cell",{"has-item":c&&!c.isRemoved,selected:s.isSelected(c),"large-item":c&&(c.width>1||c.height>1),"main-cell":c&&c.x===f&&c.y===u}]),style:_t({width:s.cellSize+"px",height:s.cellSize+"px"}),onClick:p=>s.selectItem(c,f,u)},[c&&!c.isRemoved&&c.x===f&&c.y===u?(j(),G("div",sg,[d("img",{src:s.getItemImage(c),alt:c.name,class:"item-image",style:_t(s.getItemImageStyle(c))},null,12,og)])):xe("",!0)],14,ng))),128))]))),128))],512)):(j(),G("div",rg,t[24]||(t[24]=[d("div",{class:"spinner"},null,-1),d("p",null,"加载游戏中...",-1)]))),Te(i,{"connection-line":s.gameStore.connectionLine,"cell-size":s.cellSize,"board-offset":s.boardOffset,"board-padding":s.boardPadding},null,8,["connection-line","cell-size","board-offset","board-padding"]),Te(a,{"removing-items":s.gameStore.removingItems,"cell-size":s.cellSize,"board-offset":s.boardOffset},null,8,["removing-items","cell-size","board-offset"])],512),d("div",ig,[d("button",{onClick:t[1]||(t[1]=(...l)=>s.undoMove&&s.undoMove(...l)),class:"btn-control btn-undo",disabled:!s.gameStore.canUndo,title:s.gameStore.canUndo?`撤销: ${s.gameStore.lastMoveDescription} (Ctrl+Z)`:"没有可撤销的操作"},[t[25]||(t[25]=d("span",{class:"undo-icon"},"↶",-1)),It(" 撤销 ("+se(s.gameStore.undoStepsCount)+") ",1)],8,ag),d("button",{onClick:t[2]||(t[2]=(...l)=>s.resetGame&&s.resetGame(...l)),class:"btn-control"},"重置"),d("button",{onClick:t[3]||(t[3]=(...l)=>s.saveGame&&s.saveGame(...l)),class:"btn-control"},"保存"),d("button",{onClick:t[4]||(t[4]=l=>s.showImportDialog=!0),class:"btn-control"},"导入"),d("button",{onClick:t[5]||(t[5]=(...l)=>s.shareGame&&s.shareGame(...l)),class:"btn-control btn-share",title:"分享游戏进度链接 (Ctrl+S)",disabled:s.gameStore.isSharing},[t[26]||(t[26]=d("span",{class:"share-icon"},"📤",-1)),It(" "+se(s.gameStore.isSharing?"分享中...":"分享"),1)],8,lg)])]),d("div",cg,[t[31]||(t[31]=Dn('<div class="announcement-panel" data-v-96013dd1><div class="announcement-header" data-v-96013dd1><h3 data-v-96013dd1>小里连连看规则</h3></div><div class="announcement-content" data-v-96013dd1><div class="rule-item" data-v-96013dd1><p data-v-96013dd1>每局结束连线，带出几格红就可连几个</p></div><div class="rule-item" data-v-96013dd1><p data-v-96013dd1>6格以上的红可以存一个，其他格数用不完则白送</p></div><div class="rule-item" data-v-96013dd1><p data-v-96013dd1>大格红可以降级连小格，小格红不能连大格</p></div><div class="rule-item" data-v-96013dd1><p data-v-96013dd1>如6格红可以连3对1格</p></div><div class="rule-item" data-v-96013dd1><p style="color:red;" data-v-96013dd1>注意：连线大格红需要清空其连线路径上的所有小格</p></div></div></div>',1)),d("div",ug,[t[30]||(t[30]=d("div",{class:"stored-red-header"},[d("h3",null,"暂存大红")],-1)),d("div",fg,[d("div",dg,[t[27]||(t[27]=d("span",{class:"red-label"},"6格:",-1)),d("button",{onClick:t[6]||(t[6]=l=>s.gameStore.decreaseStoredRed("red6")),class:"btn-adjust",disabled:s.gameStore.storedRed.red6<=0},"-",8,hg),d("span",pg,"("+se(s.gameStore.storedRed.red6)+")",1),d("button",{onClick:t[7]||(t[7]=l=>s.gameStore.increaseStoredRed("red6")),class:"btn-adjust"},"+")]),d("div",mg,[t[28]||(t[28]=d("span",{class:"red-label"},"9格:",-1)),d("button",{onClick:t[8]||(t[8]=l=>s.gameStore.decreaseStoredRed("red9")),class:"btn-adjust",disabled:s.gameStore.storedRed.red9<=0},"-",8,gg),d("span",yg,"("+se(s.gameStore.storedRed.red9)+")",1),d("button",{onClick:t[9]||(t[9]=l=>s.gameStore.increaseStoredRed("red9")),class:"btn-adjust"},"+")]),d("div",vg,[t[29]||(t[29]=d("span",{class:"red-label"},"12格:",-1)),d("button",{onClick:t[10]||(t[10]=l=>s.gameStore.decreaseStoredRed("red12")),class:"btn-adjust",disabled:s.gameStore.storedRed.red12<=0},"-",8,bg),d("span",Sg,"("+se(s.gameStore.storedRed.red12)+")",1),d("button",{onClick:t[11]||(t[11]=l=>s.gameStore.increaseStoredRed("red12")),class:"btn-adjust"},"+")])])])])])])])]),s.gameStore.isGameCompleted?(j(),G("div",wg,[d("div",_g,[t[35]||(t[35]=d("h2",null,"🎉 恭喜完成!",-1)),d("div",Cg,[d("div",xg,[t[33]||(t[33]=d("span",{class:"stat-label"},"得分:",-1)),d("span",Eg,se(s.gameStore.score),1)]),d("div",Rg,[t[34]||(t[34]=d("span",{class:"stat-label"},"难度:",-1)),d("span",Tg,se(s.gameStore.difficultyName),1)])]),d("div",Ag,[d("button",{onClick:t[12]||(t[12]=(...l)=>s.shareGame&&s.shareGame(...l)),class:"btn-share-complete",disabled:s.gameStore.isSharing}," 📤 "+se(s.gameStore.isSharing?"分享中...":"分享进度"),9,Pg),d("button",{onClick:t[13]||(t[13]=(...l)=>s.resetGame&&s.resetGame(...l)),class:"btn-confirm"},"再来一局"),d("button",{onClick:t[14]||(t[14]=(...l)=>s.backToHome&&s.backToHome(...l)),class:"btn-cancel"},"返回首页")])])])):xe("",!0),s.showImportDialog?(j(),G("div",{key:1,class:"dialog-overlay",onClick:t[20]||(t[20]=(...l)=>s.closeImportDialog&&s.closeImportDialog(...l))},[d("div",{class:"dialog",onClick:t[19]||(t[19]=Kt(()=>{},["stop"]))},[t[36]||(t[36]=d("h3",null,"导入游戏进度",-1)),Wt(d("input",{"onUpdate:modelValue":t[15]||(t[15]=l=>s.importCode=l),type:"text",placeholder:"请输入进度代码",onKeyup:t[16]||(t[16]=Mn((...l)=>s.importGame&&s.importGame(...l),["enter"]))},null,544),[[rn,s.importCode]]),d("div",Ig,[d("button",{onClick:t[17]||(t[17]=(...l)=>s.closeImportDialog&&s.closeImportDialog(...l)),class:"btn-cancel"},"取消"),d("button",{onClick:t[18]||(t[18]=(...l)=>s.importGame&&s.importGame(...l)),class:"btn-confirm",disabled:!s.importCode.trim()}," 导入 ",8,Og)])])])):xe("",!0),s.showSaveSuccess?(j(),G("div",kg,[d("div",Lg,[t[38]||(t[38]=d("h3",null,"保存成功!",-1)),d("p",null,[t[37]||(t[37]=It("进度代码: ",-1)),d("strong",null,se(s.savedCode),1)]),d("button",{onClick:t[21]||(t[21]=(...l)=>s.copySaveCode&&s.copySaveCode(...l)),class:"btn-copy"},"复制代码"),d("button",{onClick:t[22]||(t[22]=l=>s.showSaveSuccess=!1),class:"btn-close"},"关闭")])])):xe("",!0)],64)}const Dg=Nt(Hm,[["render",Ng],["__scopeId","data-v-96013dd1"]]);class Mg{constructor(){this.audioContext=null,this.isAudioSupported="speechSynthesis"in window,this.voices=[],this.preferredVoice=null,this.initVoices()}initVoices(){if(!this.isAudioSupported)return;const t=()=>{this.voices=speechSynthesis.getVoices(),this.selectPreferredVoice()};t(),speechSynthesis.onvoiceschanged!==void 0&&(speechSynthesis.onvoiceschanged=t)}selectPreferredVoice(){if(this.voices.length===0)return;const t=this.voices.filter(n=>n.lang.includes("zh")||n.name.toLowerCase().includes("chinese")||n.name.includes("中文"));t.length>0?this.preferredVoice=t.find(n=>n.lang.includes("zh-CN")||n.name.includes("普通话"))||t[0]:this.preferredVoice=this.voices[0]}announceResult(t,n={}){return this.isAudioSupported?new Promise((s,o)=>{try{speechSynthesis.cancel();const r=new SpeechSynthesisUtterance;r.text=n.prefix?`${n.prefix}${t}`:`${t}`,r.lang=n.lang||"zh-CN",r.rate=n.rate||.8,r.pitch=n.pitch||1.2,r.volume=n.volume||1,this.preferredVoice&&(r.voice=this.preferredVoice),r.onend=()=>{s()},r.onerror=i=>{console.error("语音播报失败:",i.error),o(i.error)},r.onstart=()=>{},speechSynthesis.speak(r)}catch(r){console.error("创建语音播报失败:",r),o(r)}}):(console.warn("浏览器不支持语音合成"),Promise.resolve())}playSpinSound(){if(!this.audioContext)try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(t){console.warn("无法创建音频上下文:",t);return}try{const t=this.audioContext.createOscillator(),n=this.audioContext.createGain();t.connect(n),n.connect(this.audioContext.destination),t.type="sine",t.frequency.setValueAtTime(200,this.audioContext.currentTime),t.frequency.exponentialRampToValueAtTime(100,this.audioContext.currentTime+.1),n.gain.setValueAtTime(.1,this.audioContext.currentTime),n.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+.1),t.start(this.audioContext.currentTime),t.stop(this.audioContext.currentTime+.1)}catch(t){console.warn("播放旋转音效失败:",t)}}playResultSound(){if(!this.audioContext)try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(t){console.warn("无法创建音频上下文:",t);return}try{const t=this.audioContext.createOscillator(),n=this.audioContext.createGain();t.connect(n),n.connect(this.audioContext.destination),t.type="sine",t.frequency.setValueAtTime(400,this.audioContext.currentTime),t.frequency.exponentialRampToValueAtTime(800,this.audioContext.currentTime+.3),n.gain.setValueAtTime(.2,this.audioContext.currentTime),n.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+.3),t.start(this.audioContext.currentTime),t.stop(this.audioContext.currentTime+.3)}catch(t){console.warn("播放结果音效失败:",t)}}stopAll(){if(this.isAudioSupported&&speechSynthesis.cancel(),this.audioContext)try{this.audioContext.close(),this.audioContext=null}catch(t){console.warn("关闭音频上下文失败:",t)}}checkAudioSupport(){return{speechSynthesis:"speechSynthesis"in window,audioContext:!!(window.AudioContext||window.webkitAudioContext),voices:this.voices.length>0,preferredVoice:this.preferredVoice?this.preferredVoice.name:null}}getAvailableVoices(){return this.voices.map(t=>({name:t.name,lang:t.lang,localService:t.localService,default:t.default}))}}const Oo=new Mg,wc=Tl("wheel",{state:()=>({segments:["苹果","香蕉","橙子","葡萄","草莓","西瓜","桃子","梨","菠萝","芒果","樱桃","柠檬","猕猴桃","蓝莓","石榴","椰子"],isSpinning:!1,currentRotation:0,selectedSegment:null,selectedIndex:-1,spinHistory:[],audioEnabled:!0,spinDuration:3e3,minSpins:5,maxSpins:8,storageKey:"wheelGameData"}),getters:{segmentAngle(){return 360/this.segments.length},segmentConfigs(){const e=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA","#F1948A","#85C1E9","#D7BDE2","#A3E4D7","#F9E79F","#D5A6BD","#AED6F1","#A9DFBF"];return this.segments.map((t,n)=>({id:n,text:t,startAngle:n*this.segmentAngle,endAngle:(n+1)*this.segmentAngle,color:e[n%e.length]}))},getPointerSegmentIndex(){const e=(360-this.currentRotation%360+360)%360;return Math.floor(e/this.segmentAngle)%this.segments.length}},actions:{async spinWheel(){if(this.isSpinning)return;this.isSpinning=!0,this.selectedSegment=null,this.selectedIndex=-1,this.audioEnabled&&Oo.playSpinSound();const e=this.minSpins+Math.random()*(this.maxSpins-this.minSpins),t=Math.random()*360,n=this.segmentAngle/2,s=Math.floor(t/this.segmentAngle)*this.segmentAngle+n,o=e*360+s;this.currentRotation+=o,setTimeout(()=>{this.isSpinning=!1,this.selectedIndex=this.getPointerSegmentIndex,this.selectedSegment=this.segments[this.selectedIndex],this.addToHistory(this.selectedSegment),this.audioEnabled&&(Oo.playResultSound(),setTimeout(()=>{this.announceResult(this.selectedSegment)},500)),this.saveToLocalStorage()},this.spinDuration)},addToHistory(e){const t={id:Date.now(),result:e,timestamp:new Date().toLocaleString("zh-CN"),date:new Date};this.spinHistory.unshift(t),this.spinHistory.length>100&&(this.spinHistory=this.spinHistory.slice(0,100))},async announceResult(e){try{await Oo.announceResult(e,{prefix:"",rate:.8,pitch:1.2})}catch(t){console.error("播报失败:",t)}},toggleAudio(){this.audioEnabled=!this.audioEnabled,this.saveToLocalStorage()},updateSegments(e){if(e.length<2||e.length>40)throw new Error("段落数量必须在2-40之间");this.segments=[...e],this.currentRotation=0,this.selectedSegment=null,this.selectedIndex=-1,this.saveToLocalStorage()},clearHistory(){this.spinHistory=[],this.saveToLocalStorage()},resetGame(){this.currentRotation=0,this.selectedSegment=null,this.selectedIndex=-1,this.spinHistory=[],this.isSpinning=!1,this.saveToLocalStorage()},saveToLocalStorage(){try{const e={segments:this.segments,currentRotation:this.currentRotation,selectedSegment:this.selectedSegment,selectedIndex:this.selectedIndex,spinHistory:this.spinHistory,audioEnabled:this.audioEnabled,lastSaved:new Date().toISOString()};localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存游戏数据失败:",e)}},loadFromLocalStorage(){try{const e=localStorage.getItem(this.storageKey);if(e){const t=JSON.parse(e);return t.segments&&(this.segments=t.segments),t.currentRotation!==void 0&&(this.currentRotation=t.currentRotation),t.selectedSegment&&(this.selectedSegment=t.selectedSegment),t.selectedIndex!==void 0&&(this.selectedIndex=t.selectedIndex),t.spinHistory&&(this.spinHistory=t.spinHistory),t.audioEnabled!==void 0&&(this.audioEnabled=t.audioEnabled),!0}return!1}catch(e){return console.error("加载游戏数据失败:",e),!1}},clearLocalStorage(){try{localStorage.removeItem(this.storageKey)}catch(e){console.error("清除本地存储失败:",e)}}}});const Hg={name:"SpinWheel",emits:["spin"],setup(e,{emit:t}){const n=wc(),s=()=>{n.isSpinning||t("spin")},o=c=>{var y;if(!c)return"16px";const f=c.length,p=((y=n.segmentConfigs)==null?void 0:y.length)||8;let g=20;return p>16?g=14:p>12?g=16:p>8&&(g=18),f<=2?`${g}px`:f<=3?`${Math.max(g-1,12)}px`:f<=4?`${Math.max(g-2,12)}px`:f<=5?`${Math.max(g-3,12)}px`:`${Math.max(g-4,10)}px`},r=c=>parseInt(o(c))*1.2,i=c=>{const f=c*Math.PI/180,p=50+50*Math.cos(f-Math.PI/2),g=50+50*Math.sin(f-Math.PI/2);return`polygon(50% 50%, 50% 0%, ${p}% ${g}%)`},a=c=>{const S=360/n.segmentConfigs.length,w=c*S-90,k=w+S,T=w*Math.PI/180,E=k*Math.PI/180,P=200+180*Math.cos(T),U=200+180*Math.sin(T),Q=200+180*Math.cos(E),Y=200+180*Math.sin(E),q=200+60*Math.cos(E),x=200+60*Math.sin(E),z=200+60*Math.cos(T),ne=200+60*Math.sin(T),L=S>180?1:0;return`M ${P} ${U} A 180 180 0 ${L} 1 ${Q} ${Y} L ${q} ${x} A 60 60 0 ${L} 0 ${z} ${ne} Z`},l=c=>{const y=360/n.segmentConfigs.length,w=(c*y+y/2-90)*Math.PI/180;return{x:200+110*Math.cos(w),y:200+110*Math.sin(w)}};return{wheelStore:n,handleCenterClick:s,getTextSize:o,getCharSpacing:r,getSegmentClipPath:i,getSegmentPath:a,getTextPosition:l,getTextTransform:c=>{const f=360/n.segmentConfigs.length,p=c*f+f/2-90,g=l(c);let y=p+90;return y=(y%360+360)%360,`rotate(${y}, ${g.x}, ${g.y})`}}}},Fg={class:"spin-wheel-container"},Ug={class:"wheel-frame"},Vg={class:"wheel-svg",viewBox:"0 0 400 400"},Bg=["d","fill"],jg={key:0},Gg=["x","y","transform"],$g={class:"center-content"},Kg={class:"center-icon"},Wg={class:"center-text"},zg={key:0,class:"result-highlight"};function qg(e,t,n,s,o,r){return j(),G("div",Fg,[d("div",Ug,[t[1]||(t[1]=d("div",{class:"pointer"},[d("div",{class:"pointer-triangle"})],-1)),d("div",{class:We(["wheel",{spinning:s.wheelStore.isSpinning,"has-result":s.wheelStore.selectedSegment}]),style:_t({transform:`rotate(${s.wheelStore.currentRotation}deg)`})},[(j(),G("svg",Vg,[(j(!0),G(Ce,null,nt(s.wheelStore.segmentConfigs,(i,a)=>(j(),G("g",{key:i.id},[d("path",{d:s.getSegmentPath(a),fill:i.color,class:We([{"segment-selected":s.wheelStore.selectedIndex===a&&!s.wheelStore.isSpinning,"segment-highlighted":s.wheelStore.selectedIndex===a},"wheel-segment-path"]),stroke:"rgba(255,255,255,0.2)","stroke-width":"1"},null,10,Bg),i.text?(j(),G("g",jg,[(j(!0),G(Ce,null,nt(i.text,(l,u)=>(j(),G("text",{key:u,x:s.getTextPosition(a).x,y:s.getTextPosition(a).y+(u-(i.text.length-1)/2)*s.getCharSpacing(i.text),transform:s.getTextTransform(a),class:"segment-text-svg",style:_t({fontSize:s.getTextSize(i.text)}),"text-anchor":"middle","dominant-baseline":"central",fill:"#ffffff",stroke:"#333333","stroke-width":"0.2","font-weight":"600","font-family":"Arial, Microsoft YaHei, sans-serif"},se(l),13,Gg))),128))])):xe("",!0)]))),128))])),d("div",{class:We(["wheel-center",{clickable:!s.wheelStore.isSpinning}]),onClick:t[0]||(t[0]=(...i)=>s.handleCenterClick&&s.handleCenterClick(...i))},[d("div",$g,[d("div",Kg,se(s.wheelStore.isSpinning?"⏳":"🎯"),1),d("div",Wg,se(s.wheelStore.isSpinning?"转动中":"SPIN"),1)])],2)],6),t[2]||(t[2]=d("div",{class:"wheel-shadow"},null,-1))]),s.wheelStore.selectedSegment&&!s.wheelStore.isSpinning?(j(),G("div",zg)):xe("",!0)])}const Jg=Nt(Hg,[["render",qg],["__scopeId","data-v-78ecf858"]]);const Xg={name:"NewWheelGame",components:{SpinWheel:Jg},setup(){const e=wc(),t=oe(!1),n=oe(!1),s=oe(""),o=oe(0),r=oe([{name:"大红转盘",icon:"🔴",segments:["微型反应炉","碳纤维板","刀片服务器","万金泪冠","电台","喇叭机","火箭燃油","步战车","炮弹","纵横","人头像","坦克","非洲之心","暖气片","除颤仪","医疗机器人","高速阵列","ECMO","复苏呼吸机","动力电池组","装甲车电池","强力吸尘器","超算","黑匣子","笔记本","海洋之泪","浮力设备","扫地机器人","云存储","绝密服务器"]},{name:"小红转盘",icon:"🟠",segments:["无人机","小终端","大终端","显卡","呼吸机","香槟","鱼子酱","滑膛枪","天圆地方","瞪羚","化石","金条","怀表","机械表","量子存储","实验数据","咖啡豆","摄影机","锅"]},{name:"航天房卡",icon:"🚀",segments:["总裁会议室","蓝室数据中心","组装车间","蓝室玻璃房","蓝室核心","黒室服务器","3号宿舍里屋","中控室三楼","浮力室医务室"]},{name:"巴克什房卡",icon:"🏠",segments:["博物馆监控室","西城民宅","东城民宅","总裁1间","地下金库1间","旅店用餐间","Relink","博物馆展览套间","老浴场贵宾室","博物馆废弃展厅","医疗会议室","1号审讯室","生物数据机房","海洋监测厅","老浴场餐厅","门诊室","旅店员工休息室"]}]),i=Ee(()=>n.value?e.spinHistory:e.spinHistory.slice(0,10)),a=E=>{o.value=E;const P=r.value[E];e.updateSegments(P.segments),e.clearHistory()},l=()=>{a(0)},u=()=>{e.spinWheel()},c=()=>{e.toggleAudio()},f=()=>{confirm("确定要清除所有抽奖记录吗？此操作不可恢复。")&&(e.clearHistory(),n.value=!1)},p=()=>{confirm("确定要重置游戏吗？这将清除所有数据。")&&(e.resetGame(),n.value=!1)},g=()=>{window.location.href="/"},y=()=>{t.value=!1,s.value=e.segments.join(`
`)},S=()=>{const E=s.value.split(`
`).map(P=>P.trim()).filter(P=>P.length>0);if(E.length<2){alert("至少需要2个选项！");return}if(E.length>40){alert("最多支持40个选项！");return}try{e.updateSegments(E),y(),alert("设置已应用！")}catch(P){alert("应用设置失败："+P.message)}},w=()=>{if(confirm("确定要恢复默认转盘内容吗？")){const E=["苹果","香蕉","橙子","葡萄","草莓","西瓜","桃子","梨","菠萝","芒果","樱桃","柠檬","猕猴桃","蓝莓","石榴","椰子"];e.updateSegments(E),s.value=E.join(`
`),alert("已恢复默认设置！")}},k=()=>{try{const E={exportTime:new Date().toLocaleString("zh-CN"),totalCount:e.spinHistory.length,history:e.spinHistory},P=new Blob([JSON.stringify(E,null,2)],{type:"application/json"}),U=URL.createObjectURL(P),Q=document.createElement("a");Q.href=U,Q.download=`转盘游戏记录_${new Date().toISOString().slice(0,10)}.json`,Q.click(),URL.revokeObjectURL(U)}catch(E){alert("导出失败："+E.message)}},T=()=>new Date().toLocaleString("zh-CN");return dn(()=>{e.loadFromLocalStorage()?confirm(`检测到之前的游戏数据，是否要加载？

点击"确定"加载之前的数据
点击"取消"开始新游戏`)||(e.resetGame(),l()):l(),s.value=e.segments.join(`
`)}),{wheelStore:e,showSettings:t,showAllHistory:n,customSegments:s,displayHistory:i,wheelOptions:r,selectedWheelIndex:o,selectWheel:a,spinWheel:u,toggleAudio:c,clearHistory:f,resetGame:p,goHome:g,closeSettings:y,applyCustomSegments:S,resetToDefault:w,exportHistory:k,getCurrentTime:T}}},Yg={class:"new-wheel-game"},Qg={class:"game-header"},Zg={class:"control-buttons"},ey=["title"],ty={class:"game-main"},ny={class:"game-section"},sy={class:"wheel-history-container"},oy={class:"wheel-selector-panel"},ry={class:"selector-card"},iy={class:"selector-options"},ay=["onClick"],ly={class:"option-icon"},cy={class:"option-name"},uy={class:"wheel-section"},fy={class:"wheel-card"},dy={class:"right-panel"},hy={class:"result-panel"},py={key:0,class:"result-section"},my={class:"result-content"},gy={class:"result-text"},yy={class:"result-time"},vy={class:"history-panel"},by={class:"history-header"},Sy={class:"history-count"},wy={class:"history-content"},_y={key:0,class:"no-history"},Cy={key:1,class:"history-list"},xy={class:"history-result"},Ey={class:"result-badge"},Ry={class:"history-meta"},Ty={class:"history-time"},Ay={class:"history-index"},Py={key:0,class:"more-history"},Iy={class:"export-section"},Oy={class:"settings-header"},ky={class:"settings-body"},Ly={class:"setting-group"},Ny={class:"setting-group"},Dy={class:"setting-options"},My={class:"option-item"},Hy={class:"settings-footer"};function Fy(e,t,n,s,o,r){const i=Is("SpinWheel");return j(),G("div",Yg,[d("div",Qg,[d("div",Zg,[d("button",{onClick:t[0]||(t[0]=(...a)=>s.goHome&&s.goHome(...a)),class:"control-btn home",title:"返回首页"},t[16]||(t[16]=[d("span",null,"首页",-1)])),d("button",{onClick:t[1]||(t[1]=(...a)=>s.toggleAudio&&s.toggleAudio(...a)),class:We(["control-btn",{active:s.wheelStore.audioEnabled}]),title:s.wheelStore.audioEnabled?"关闭音效":"开启音效"},[It(se(s.wheelStore.audioEnabled?"🔊":"🔇")+" ",1),t[17]||(t[17]=d("span",null,"音效",-1))],10,ey),d("button",{onClick:t[2]||(t[2]=a=>s.showSettings=!0),class:"control-btn",title:"转盘设置"},t[18]||(t[18]=[d("span",null,"设置",-1)])),d("button",{onClick:t[3]||(t[3]=(...a)=>s.clearHistory&&s.clearHistory(...a)),class:"control-btn danger",title:"清除历史记录"},t[19]||(t[19]=[d("span",null,"清除",-1)])),d("button",{onClick:t[4]||(t[4]=(...a)=>s.resetGame&&s.resetGame(...a)),class:"control-btn warning",title:"重置游戏"},t[20]||(t[20]=[d("span",null,"重置",-1)]))])]),d("div",ty,[d("div",ny,[t[25]||(t[25]=d("div",{class:"game-title-section"},[d("h1",null,"🎯 幸运转盘")],-1)),d("div",sy,[d("div",oy,[d("div",ry,[t[21]||(t[21]=d("div",{class:"selector-header"},[d("h3",null,"🎯 选择转盘")],-1)),d("div",iy,[(j(!0),G(Ce,null,nt(s.wheelOptions,(a,l)=>(j(),G("div",{key:l,class:We(["selector-option",{active:s.selectedWheelIndex===l}]),onClick:u=>s.selectWheel(l)},[d("div",ly,se(a.icon),1),d("div",cy,se(a.name),1)],10,ay))),128))])])]),d("div",uy,[d("div",fy,[Te(i,{onSpin:s.spinWheel},null,8,["onSpin"])])]),d("div",dy,[d("div",hy,[Te(Go,{name:"result-fade"},{default:Vn(()=>[s.wheelStore.selectedSegment&&!s.wheelStore.isSpinning?(j(),G("div",py,[t[22]||(t[22]=d("div",{class:"result-header"},[d("span",{class:"result-icon"},"🎉"),d("h3",null,"恭喜您抽中了")],-1)),d("div",my,[d("div",gy,se(s.wheelStore.selectedSegment),1),d("div",yy,se(s.getCurrentTime()),1)])])):xe("",!0)]),_:1})]),d("div",vy,[d("div",by,[t[23]||(t[23]=d("h3",null,"抽奖记录",-1)),d("span",Sy,se(s.wheelStore.spinHistory.length)+" 次",1)]),d("div",wy,[s.wheelStore.spinHistory.length===0?(j(),G("div",_y,t[24]||(t[24]=[d("div",{class:"no-history-text"},"暂无抽奖记录",-1),d("div",{class:"no-history-tip"},'点击"开始转动"进行第一次抽奖吧！',-1)]))):(j(),G("div",Cy,[Te(Bf,{name:"history-item",tag:"div"},{default:Vn(()=>[(j(!0),G(Ce,null,nt(s.displayHistory,a=>(j(),G("div",{key:a.id,class:"history-item"},[d("div",xy,[d("span",Ey,se(a.result),1)]),d("div",Ry,[d("span",Ty,se(a.timestamp),1),d("span",Ay,"#"+se(s.wheelStore.spinHistory.indexOf(a)+1),1)])]))),128))]),_:1}),s.wheelStore.spinHistory.length>10?(j(),G("div",Py,[d("button",{onClick:t[5]||(t[5]=a=>s.showAllHistory=!s.showAllHistory),class:"more-btn"},se(s.showAllHistory?"收起":`还有 ${s.wheelStore.spinHistory.length-10} 条记录...`),1)])):xe("",!0),d("div",Iy,[s.wheelStore.spinHistory.length>0?(j(),G("button",{key:0,onClick:t[6]||(t[6]=(...a)=>s.exportHistory&&s.exportHistory(...a)),class:"export-btn",title:"导出记录"}," 📤 导出记录 ")):xe("",!0)])]))])])])])])]),Te(Go,{name:"modal"},{default:Vn(()=>[s.showSettings?(j(),G("div",{key:0,class:"settings-modal",onClick:t[15]||(t[15]=(...a)=>s.closeSettings&&s.closeSettings(...a))},[d("div",{class:"settings-content",onClick:t[14]||(t[14]=Kt(()=>{},["stop"]))},[d("div",Oy,[t[26]||(t[26]=d("h3",null,"转盘设置",-1)),d("button",{onClick:t[7]||(t[7]=(...a)=>s.closeSettings&&s.closeSettings(...a)),class:"close-btn"},"✕")]),d("div",ky,[d("div",Ly,[t[27]||(t[27]=d("label",{class:"setting-label"},"自定义转盘内容：",-1)),Wt(d("textarea",{"onUpdate:modelValue":t[8]||(t[8]=a=>s.customSegments=a),class:"setting-textarea",placeholder:`请输入转盘内容，每行一个项目
例如：
苹果
香蕉
橙子`,rows:"8"},null,512),[[rn,s.customSegments]]),t[28]||(t[28]=d("div",{class:"setting-tip"},[d("span",{class:"tip-icon"},"💡"),It(" 每行输入一个选项，支持2-40个选项 ")],-1))]),d("div",Ny,[t[30]||(t[30]=d("label",{class:"setting-label"},"音效设置：",-1)),d("div",Dy,[d("label",My,[Wt(d("input",{type:"checkbox","onUpdate:modelValue":t[9]||(t[9]=a=>s.wheelStore.audioEnabled=a),onChange:t[10]||(t[10]=a=>s.wheelStore.saveToLocalStorage())},null,544),[[zf,s.wheelStore.audioEnabled]]),t[29]||(t[29]=d("span",null,"启用音效和语音播报",-1))])])])]),d("div",Hy,[d("button",{onClick:t[11]||(t[11]=(...a)=>s.applyCustomSegments&&s.applyCustomSegments(...a)),class:"apply-btn"}," ✓ 应用设置 "),d("button",{onClick:t[12]||(t[12]=(...a)=>s.resetToDefault&&s.resetToDefault(...a)),class:"reset-btn"}," 🔄 恢复默认 "),d("button",{onClick:t[13]||(t[13]=(...a)=>s.closeSettings&&s.closeSettings(...a)),class:"cancel-btn"}," ✕ 取消 ")])])])):xe("",!0)]),_:1})])}const Uy=Nt(Xg,[["render",Fy],["__scopeId","data-v-756fb80f"]]);const Vy={name:"Toast",props:{message:{type:String,required:!0},type:{type:String,default:"success",validator:e=>["success","error","warning","info"].includes(e)},duration:{type:Number,default:3e3},autoClose:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:t}){const n=oe(!1),s=()=>{const i={success:"✅",error:"❌",warning:"⚠️",info:"ℹ️"};return i[e.type]||i.info},o=()=>{n.value=!0,e.autoClose&&e.duration>0&&setTimeout(()=>{r()},e.duration)},r=()=>{n.value=!1,setTimeout(()=>{t("close")},300)};return dn(()=>{o()}),{visible:n,getIcon:s,close:r}}},By={class:"toast-content"},jy={class:"toast-icon"},Gy={class:"toast-message"};function $y(e,t,n,s,o,r){return j(),ul(Go,{name:"toast"},{default:Vn(()=>[s.visible?(j(),G("div",{key:0,class:We(["toast",`toast-${n.type}`])},[d("div",By,[d("span",jy,se(s.getIcon()),1),d("span",Gy,se(n.message),1)])],2)):xe("",!0)]),_:1})}const Ky=Nt(Vy,[["render",$y],["__scopeId","data-v-042853c6"]]);class Wy{constructor(){this.toasts=[],this.container=null,this.init()}init(){this.container=document.createElement("div"),this.container.id="toast-container",this.container.style.cssText=`
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9999;
      pointer-events: none;
    `,document.body.appendChild(this.container)}show(t,n="success",s={}){const o=Date.now()+Math.random(),r=document.createElement("div");r.id=`toast-${o}`,r.style.pointerEvents="auto",this.container.appendChild(r);const i=_l(Ky,{message:t,type:n,duration:s.duration||3e3,autoClose:s.autoClose!==!1,onClose:()=>{this.remove(o,i,r)}});return i.mount(r),this.toasts.push({id:o,app:i,element:r}),o}remove(t,n,s){n&&n.unmount(),s&&s.parentNode&&s.parentNode.removeChild(s),this.toasts=this.toasts.filter(o=>o.id!==t)}success(t,n={}){return this.show(t,"success",n)}error(t,n={}){return this.show(t,"error",n)}warning(t,n={}){return this.show(t,"warning",n)}info(t,n={}){return this.show(t,"info",n)}clear(){this.toasts.forEach(t=>{this.remove(t.id,t.app,t.element)}),this.toasts=[]}destroy(){this.clear(),this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container)}}const mn=new Wy,Re={success:(e,t)=>mn.success(e,t),error:(e,t)=>mn.error(e,t),warning:(e,t)=>mn.warning(e,t),info:(e,t)=>mn.info(e,t),show:(e,t,n)=>mn.show(e,t,n),clear:()=>mn.clear()},zy=Object.freeze(Object.defineProperty({__proto__:null,default:Re,toast:Re},Symbol.toStringTag,{value:"Module"}));const qy={name:"ItemSelectionGame",setup(){const e=Vl(),t=Cr(),n=oe(""),s=oe(hc("玩家")),o=oe([]),r=oe(!1),i=oe("加载中..."),a=oe(!1),l=oe(""),u=oe(!1),c=oe(""),f=oe(!1),p=oe(!1),g=oe([]),y=oe("normal"),S=Ee(()=>o.value.filter(I=>I.status==="pending").length),w=Ee(()=>o.value.filter(I=>I.status==="confirmed").length),k=Ee(()=>{const I=g.value.find($=>$.level===y.value);return I?I.name:"普通版"}),T=I=>!I||!I.imagePath?"/images/default-item.png":I.imagePath,E=I=>{I.target.src="/images/default-item.png"},P=I=>{I.status!=="confirmed"&&(I.status=I.status==="pending"?"default":"pending",f.value=!0)},U=async()=>{if(!n.value){console.warn("没有sessionId，无法保存游戏状态");return}try{await Ye.updateGameState(n.value,o.value),f.value=!1,console.log("游戏状态已保存到后端")}catch(I){console.error("保存到后端失败，使用本地存储:",I);try{const $={sessionId:n.value,playerName:s.value,items:o.value,lastSaved:new Date().toISOString()};localStorage.setItem(`item-selection-${n.value}`,JSON.stringify($)),f.value=!1,console.log("游戏状态已保存到本地存储")}catch($){console.error("本地存储也失败:",$),Re.error("保存失败: "+I.message)}}},Q=async()=>{if(S.value===0){Re.warning("没有待确认的物品");return}const I=S.value;try{r.value=!0,i.value="确认并保存中...",await Ye.confirmSelection(n.value),o.value.forEach($=>{$.status==="pending"&&($.status="confirmed")});try{await U(),Re.success(`已确认 ${I} 个物品并保存`)}catch($){console.error("自动保存失败:",$),f.value=!1,Re.success(`已确认 ${I} 个物品（保存失败）`)}}catch($){console.error("确认选择失败:",$),Re.error("确认失败: "+$.message)}finally{r.value=!1}},Y=async()=>{if(confirm("确定要重置游戏吗？这将清除所有选择状态。"))try{r.value=!0,i.value="重置中...",await Ye.resetGame(n.value),o.value.forEach(I=>{I.status="default"}),f.value=!1,Re.success("游戏已重置")}catch(I){console.error("重置游戏失败:",I),Re.error("重置失败: "+I.message)}finally{r.value=!1}},q=async()=>{try{r.value=!0,i.value="保存中...";const $=(await Ye.exportGameState(n.value,o.value,s.value)).data.exportCode;c.value=$,u.value=!0,Re.success("保存成功"),console.log("保存成功，进度代码:",$)}catch(I){console.error("保存游戏失败:",I),Re.error("保存失败: "+I.message)}finally{r.value=!1}},x=async()=>{if(!l.value.trim()){Re.warning("请输入导入代码");return}try{r.value=!0,i.value="导入中...",await Ye.importGameState(n.value,l.value.trim()),await ae(),Re.success("导入成功"),z(),console.log("导入成功，进度代码:",l.value.trim())}catch(I){console.error("导入游戏失败:",I),Re.error("导入失败: "+I.message)}finally{r.value=!1}},z=()=>{a.value=!1,l.value=""},ne=async()=>{try{await navigator.clipboard.writeText(c.value),Re.success("代码已复制到剪贴板")}catch(I){console.error("复制失败:",I),Re.error("复制失败，请手动复制代码")}},L=async()=>{try{const I=await Ye.getDifficultyLevels();I.data&&I.data.levels&&(g.value=I.data.levels)}catch(I){console.error("加载难度级别失败:",I),g.value=[{level:"normal",name:"普通版",description:"标准难度",isDefault:!0},{level:"hard",name:"困难版",description:"更具挑战性",isDefault:!1}]}},ie=async I=>{var $;if(y.value!==I&&confirm("切换难度将重新开始游戏，确定要继续吗？"))try{r.value=!0,i.value="切换难度中...";const me=await Ye.createGameSession(s.value,I);if(me.data&&me.data.sessionId){n.value=me.data.sessionId,y.value=I,await ae();const Dt=(($=g.value.find(De=>De.level===I))==null?void 0:$.name)||I;Re.success(`已切换到${Dt}`)}else throw new Error("创建新游戏会话失败")}catch(me){console.error("切换难度失败:",me),Re.error("切换难度失败: "+me.message)}finally{r.value=!1}},we=I=>{switch(I){case"normal":return"🟢";case"hard":return"🔴";default:return"⚪"}},Le=()=>{t.push("/")},fe=async()=>{if(p.value)return!1;try{p.value=!0;const $=(await Ye.exportGameState(n.value,o.value,s.value)).data.exportCode,{shareGameProgress:me}=await Ms(()=>import("./shareUtils-9f678a7f.js"),[]),Dt=Z($),{copyToClipboard:De}=await Ms(()=>Promise.resolve().then(()=>_m),void 0),C=await De(Dt);return C?Re.success(`分享链接已复制到剪贴板！
其他人点击链接即可加载您的游戏进度`,{duration:3e3}):Re.error("复制失败，请手动复制链接",{duration:3e3}),C}catch(I){return console.error("分享失败:",I),Re.error("分享失败: "+I.message),!1}finally{p.value=!1}},Z=I=>`${window.location.origin}/?itemShare=${encodeURIComponent(I)}`,ae=async()=>{try{const $=(await Ye.getGameState(n.value)).data;s.value=$.playerName,$.items&&Array.isArray($.items)?o.value=$.items.map(me=>({id:me.id,name:me.name,imagePath:me.imagePath,positionX:me.positionX,positionY:me.positionY,status:me.status||"default"})):await V()}catch(I){console.error("从后端加载游戏状态失败:",I),await V()}},V=async()=>{try{const I=localStorage.getItem(`item-selection-${n.value}`);if(I){const $=JSON.parse(I);s.value=$.playerName||"本地玩家",o.value=$.items||[],console.log("从本地存储加载游戏状态"),Re.success("从本地存储恢复游戏状态")}else await le()}catch(I){console.error("从本地存储加载失败:",I),await le()}},le=async()=>{try{const I=await Ye.createGameSession("演示玩家");I.data&&I.data.items?(o.value=I.data.items.map($=>({id:$.id,name:$.name,imagePath:$.imagePath,positionX:$.positionX,positionY:$.positionY,status:$.status||"default"})),n.value=I.data.sessionId,s.value=I.data.playerName):(console.error("无法获取默认物品数据"),o.value=[])}catch(I){console.error("获取默认物品数据失败:",I),o.value=[]}},Ae=()=>{if(f.value){const I={boardId:1,width:8,height:8,items:o.value.map($=>({id:$.id,itemId:$.id,name:$.name,x:$.positionX,y:$.positionY,width:1,height:1,imagePath:$.imagePath,isRemoved:$.status==="confirmed"})),storedRed:{red6:0,red9:0,red12:0}};navigator.sendBeacon("/api/session/update",JSON.stringify({sessionId:n.value,boardState:I,score:w.value*10+S.value*5,isCompleted:!1}))}};return dn(async()=>{if(await L(),n.value=e.params.sessionId,n.value){r.value=!0,i.value="加载游戏状态...";try{await ae()}finally{r.value=!1}}else{r.value=!0,i.value="创建新游戏...";try{await le()}finally{r.value=!1}}window.addEventListener("beforeunload",Ae)}),Xs(()=>{window.removeEventListener("beforeunload",Ae)}),{sessionId:n,playerName:s,items:o,isLoading:r,loadingText:i,showImportDialog:a,importCode:l,showSaveSuccess:u,savedCode:c,hasUnsavedChanges:f,isSharing:p,difficultyLevels:g,currentDifficulty:y,currentDifficultyName:k,pendingCount:S,confirmedCount:w,getImageUrl:T,handleImageError:E,toggleItemStatus:P,confirmSelection:Q,resetGame:Y,exportGame:q,importGame:x,closeImportDialog:z,copySaveCode:ne,selectDifficulty:ie,getDifficultyIcon:we,goToHome:Le,shareGame:fe}}},Jy={class:"item-selection-game"},Xy={class:"game-header"},Yy={class:"header-left"},Qy={class:"player-info"},Zy={class:"game-stats"},ev={class:"stat-item"},tv={class:"stat-value pending"},nv={class:"stat-item"},sv={class:"stat-value confirmed"},ov={class:"stat-item"},rv={class:"game-main"},iv={class:"game-content-container"},av={class:"difficulty-selector-panel"},lv={class:"selector-card"},cv={class:"selector-options"},uv=["onClick"],fv={class:"option-icon"},dv={class:"option-info"},hv={class:"option-name"},pv={class:"option-desc"},mv={class:"game-area"},gv={key:0,class:"game-grid"},yv=["onClick"],vv={key:0,class:"status-label"},bv={class:"item-name"},Sv={class:"game-controls"},wv=["disabled"],_v=["disabled"],Cv={class:"dialog-buttons"},xv=["disabled"],Ev={key:1,class:"save-success"},Rv={class:"success-content"},Tv={key:2,class:"loading"};function Av(e,t,n,s,o,r){return j(),G("div",Jy,[d("div",Xy,[d("div",Yy,[d("button",{class:"home-btn",onClick:t[0]||(t[0]=(...i)=>s.goToHome&&s.goToHome(...i))},t[13]||(t[13]=[d("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[d("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),d("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),It(" 返回首页 ",-1)])),d("div",Qy,[t[14]||(t[14]=d("h2",null,"小里Bingo 系统",-1)),d("p",null,"玩家: "+se(s.playerName),1)])]),d("div",Zy,[d("div",ev,[t[15]||(t[15]=d("span",{class:"stat-label"},"待确认:",-1)),d("span",tv,se(s.pendingCount),1)]),d("div",nv,[t[16]||(t[16]=d("span",{class:"stat-label"},"已确认:",-1)),d("span",sv,se(s.confirmedCount),1)]),d("div",ov,[t[17]||(t[17]=d("span",{class:"stat-label"},"状态:",-1)),d("span",{class:We(["stat-value",{unsaved:s.hasUnsavedChanges,saved:!s.hasUnsavedChanges}])},se(s.hasUnsavedChanges?"未保存":"已保存"),3)])])]),d("div",rv,[d("div",iv,[d("div",av,[d("div",lv,[t[18]||(t[18]=d("div",{class:"selector-header"},[d("h3",null,"🎯 选择难度")],-1)),d("div",cv,[(j(!0),G(Ce,null,nt(s.difficultyLevels,i=>(j(),G("div",{key:i.level,class:We(["selector-option",{active:s.currentDifficulty===i.level}]),onClick:a=>s.selectDifficulty(i.level)},[d("div",fv,se(s.getDifficultyIcon(i.level)),1),d("div",dv,[d("div",hv,se(i.name),1),d("div",pv,se(i.description),1)])],10,uv))),128))])])]),d("div",mv,[s.items.length>0?(j(),G("div",gv,[(j(!0),G(Ce,null,nt(s.items,i=>(j(),G("div",{key:i.id,class:We(["item-card",{pending:i.status==="pending",confirmed:i.status==="confirmed"}]),style:_t({backgroundImage:`url(${s.getImageUrl(i)})`}),onClick:a=>s.toggleItemStatus(i)},[i.status==="pending"?(j(),G("div",vv," 待确认 ")):xe("",!0),d("div",bv,se(i.name),1)],14,yv))),128))])):xe("",!0),d("div",Sv,[d("button",{class:"control-btn confirm-btn",onClick:t[1]||(t[1]=(...i)=>s.confirmSelection&&s.confirmSelection(...i)),disabled:s.pendingCount===0}," 确认 ("+se(s.pendingCount)+") ",9,wv),d("button",{class:"control-btn reset-btn",onClick:t[2]||(t[2]=(...i)=>s.resetGame&&s.resetGame(...i))}," 重置 "),d("button",{class:"control-btn save-btn",onClick:t[3]||(t[3]=(...i)=>s.exportGame&&s.exportGame(...i))}," 保存 "),d("button",{class:"control-btn import-btn",onClick:t[4]||(t[4]=i=>s.showImportDialog=!0)}," 导入 "),d("button",{class:"control-btn share-btn",onClick:t[5]||(t[5]=(...i)=>s.shareGame&&s.shareGame(...i)),disabled:s.isSharing},[t[19]||(t[19]=d("span",{class:"share-icon"},"📤",-1)),It(" "+se(s.isSharing?"分享中...":"分享"),1)],8,_v)])])])]),s.showImportDialog?(j(),G("div",{key:0,class:"dialog-overlay",onClick:t[10]||(t[10]=(...i)=>s.closeImportDialog&&s.closeImportDialog(...i))},[d("div",{class:"dialog",onClick:t[9]||(t[9]=Kt(()=>{},["stop"]))},[t[20]||(t[20]=d("h3",null,"导入游戏状态",-1)),Wt(d("textarea",{"onUpdate:modelValue":t[6]||(t[6]=i=>s.importCode=i),placeholder:"请粘贴导入代码",rows:"4"},null,512),[[rn,s.importCode]]),d("div",Cv,[d("button",{onClick:t[7]||(t[7]=(...i)=>s.closeImportDialog&&s.closeImportDialog(...i)),class:"btn-cancel"},"取消"),d("button",{onClick:t[8]||(t[8]=(...i)=>s.importGame&&s.importGame(...i)),class:"btn-confirm",disabled:!s.importCode.trim()}," 导入 ",8,xv)])])])):xe("",!0),s.showSaveSuccess?(j(),G("div",Ev,[d("div",Rv,[t[22]||(t[22]=d("h3",null,"保存成功!",-1)),d("p",null,[t[21]||(t[21]=It("进度代码: ",-1)),d("strong",null,se(s.savedCode),1)]),d("button",{onClick:t[11]||(t[11]=(...i)=>s.copySaveCode&&s.copySaveCode(...i)),class:"btn-copy"},"复制代码"),d("button",{onClick:t[12]||(t[12]=i=>s.showSaveSuccess=!1),class:"btn-close"},"关闭")])])):xe("",!0),s.isLoading?(j(),G("div",Tv,[t[23]||(t[23]=d("div",{class:"spinner"},null,-1)),d("p",null,se(s.loadingText),1)])):xe("",!0)])}const Pv=Nt(qy,[["render",Av],["__scopeId","data-v-8ea67ee3"]]),Iv=[{path:"/",name:"Home",component:Sm},{path:"/game/:sessionId?",name:"Game",component:Dg,props:!0},{path:"/wheel",name:"WheelGame",component:Uy},{path:"/item-selection/:sessionId",name:"ItemSelectionGame",component:Pv,props:!0}],Ov=uh({history:Ud(),routes:Iv}),Or=_l(mh),kv=sd();Or.use(kv);Or.use(Ov);Or.mount("#app");export{vc as c,Re as t};
