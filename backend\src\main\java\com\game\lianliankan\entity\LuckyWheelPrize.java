package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "lucky_wheel_prizes",
       indexes = {
           @Index(name = "idx_prize_active", columnList = "is_active"),
           @Index(name = "idx_prize_probability", columnList = "probability")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyWheelPrize {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "image_path", length = 200)
    private String imagePath;
    
    @Column(name = "total_quantity", nullable = false)
    private Integer totalQuantity;
    
    @Column(name = "remaining_quantity", nullable = false)
    private Integer remainingQuantity;
    
    @Column(name = "probability", nullable = false, precision = 5, scale = 4)
    private BigDecimal probability; // 概率，范围0-1，精度到万分位
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (remainingQuantity == null) {
            remainingQuantity = totalQuantity;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查奖品是否有库存
     */
    public boolean hasStock() {
        return remainingQuantity != null && remainingQuantity > 0;
    }
    
    /**
     * 减少库存
     */
    public void decreaseStock() {
        if (hasStock()) {
            remainingQuantity--;
        }
    }
}
