package com.game.lianliankan.service;

import com.game.lianliankan.dto.LuckyWheelDTO;
import com.game.lianliankan.entity.*;
import com.game.lianliankan.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class LuckyWheelService {
    
    private final LuckyWheelPrizeRepository prizeRepository;
    private final LuckyWheelCDKRepository cdkRepository;
    private final LuckyWheelSpinResultRepository spinResultRepository;
    private final LuckyWheelAdminRepository adminRepository;
    private final GameSessionRepository gameSessionRepository;
    
    private final SecureRandom random = new SecureRandom();
    
    // ==================== 奖品管理 ====================
    
    /**
     * 获取所有奖品
     */
    public List<LuckyWheelDTO.PrizeInfo> getAllPrizes() {
        return prizeRepository.findAllByOrderBySortOrderAsc()
                .stream()
                .map(this::convertToPrizeInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取可用奖品（用于转盘显示）
     */
    public List<LuckyWheelDTO.PrizeInfo> getAvailablePrizes() {
        return prizeRepository.findAvailablePrizes()
                .stream()
                .map(this::convertToPrizeInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 创建奖品
     */
    @Transactional
    public LuckyWheelDTO.PrizeInfo createPrize(LuckyWheelDTO.PrizeCreateRequest request) {
        LuckyWheelPrize prize = new LuckyWheelPrize();
        prize.setName(request.getName());
        prize.setDescription(request.getDescription());
        prize.setImagePath(request.getImagePath());
        prize.setTotalQuantity(request.getTotalQuantity());
        prize.setRemainingQuantity(request.getTotalQuantity());
        prize.setProbability(request.getProbability());
        prize.setSortOrder(request.getSortOrder());
        prize.setIsActive(true);
        
        prize = prizeRepository.save(prize);
        log.info("创建奖品成功: {}", prize.getName());
        
        return convertToPrizeInfo(prize);
    }
    
    /**
     * 更新奖品
     */
    @Transactional
    public LuckyWheelDTO.PrizeInfo updatePrize(LuckyWheelDTO.PrizeUpdateRequest request) {
        LuckyWheelPrize prize = prizeRepository.findById(request.getId())
                .orElseThrow(() -> new RuntimeException("奖品不存在"));
        
        prize.setName(request.getName());
        prize.setDescription(request.getDescription());
        prize.setImagePath(request.getImagePath());
        prize.setTotalQuantity(request.getTotalQuantity());
        prize.setProbability(request.getProbability());
        prize.setIsActive(request.getIsActive());
        prize.setSortOrder(request.getSortOrder());
        
        // 如果增加了总数量，相应增加剩余数量
        if (request.getTotalQuantity() > prize.getTotalQuantity()) {
            int increase = request.getTotalQuantity() - prize.getTotalQuantity();
            prize.setRemainingQuantity(prize.getRemainingQuantity() + increase);
        }
        
        prize = prizeRepository.save(prize);
        log.info("更新奖品成功: {}", prize.getName());
        
        return convertToPrizeInfo(prize);
    }
    
    /**
     * 删除奖品
     */
    @Transactional
    public void deletePrize(Long prizeId) {
        LuckyWheelPrize prize = prizeRepository.findById(prizeId)
                .orElseThrow(() -> new RuntimeException("奖品不存在"));
        
        // 检查是否有相关的转盘记录
        long spinCount = spinResultRepository.countByPrizeIdAndIsWinningSpinTrue(prizeId);
        if (spinCount > 0) {
            throw new RuntimeException("该奖品已有中奖记录，无法删除");
        }
        
        prizeRepository.delete(prize);
        log.info("删除奖品成功: {}", prize.getName());
    }
    
    // ==================== CDK管理 ====================
    
    /**
     * 批量生成CDK
     */
    @Transactional
    public LuckyWheelDTO.BatchCDKResponse generateCDKs(LuckyWheelDTO.CDKCreateRequest request, String createdBy) {
        List<String> cdkCodes = new ArrayList<>();
        
        for (int i = 0; i < request.getCount(); i++) {
            String cdkCode = generateUniqueCDKCode(request.getPrefix());
            
            LuckyWheelCDK cdk = new LuckyWheelCDK();
            cdk.setCdkCode(cdkCode);
            cdk.setMaxSpins(request.getMaxSpins());
            cdk.setUsedSpins(0);
            cdk.setStatus("ACTIVE");
            cdk.setExpireTime(request.getExpireTime());
            cdk.setDescription(request.getDescription());
            cdk.setCreatedBy(createdBy);
            
            cdkRepository.save(cdk);
            cdkCodes.add(cdkCode);
        }
        
        log.info("批量生成CDK成功，数量: {}, 创建者: {}", request.getCount(), createdBy);
        
        return new LuckyWheelDTO.BatchCDKResponse(
                request.getCount(),
                cdkCodes,
                "成功生成 " + request.getCount() + " 个CDK"
        );
    }
    
    /**
     * 获取所有CDK
     */
    public List<LuckyWheelDTO.CDKInfo> getAllCDKs() {
        return cdkRepository.findAllByOrderByCreatedAtDesc()
                .stream()
                .map(this::convertToCDKInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证CDK
     */
    @Transactional
    public LuckyWheelDTO.CDKValidateResponse validateCDK(LuckyWheelDTO.CDKValidateRequest request) {
        Optional<LuckyWheelCDK> cdkOpt = cdkRepository.findByCdkCode(request.getCdkCode());
        
        if (cdkOpt.isEmpty()) {
            return new LuckyWheelDTO.CDKValidateResponse(
                    false, "CDK不存在", 0, null, null
            );
        }
        
        LuckyWheelCDK cdk = cdkOpt.get();
        
        if (!cdk.isAvailable()) {
            String message = "CDK不可用";
            if (cdk.isExpired()) {
                message = "CDK已过期";
            } else if (cdk.getRemainingSpins() <= 0) {
                message = "CDK次数已用完";
            } else if (!"ACTIVE".equals(cdk.getStatus())) {
                message = "CDK已被禁用";
            }
            
            return new LuckyWheelDTO.CDKValidateResponse(
                    false, message, cdk.getRemainingSpins(), cdk.getExpireTime(), null
            );
        }
        
        // 创建游戏会话
        String sessionId = generateSessionId();
        
        return new LuckyWheelDTO.CDKValidateResponse(
                true, "CDK验证成功", cdk.getRemainingSpins(), cdk.getExpireTime(), sessionId
        );
    }
    
    // ==================== 转盘游戏逻辑 ====================
    
    /**
     * 执行转盘
     */
    @Transactional
    public LuckyWheelDTO.SpinResponse spin(LuckyWheelDTO.SpinRequest request, String ipAddress, String userAgent) {
        // 验证CDK
        LuckyWheelCDK cdk = cdkRepository.findByCdkCode(request.getCdkCode())
                .orElseThrow(() -> new RuntimeException("CDK不存在"));

        if (!cdk.isAvailable()) {
            return new LuckyWheelDTO.SpinResponse(
                    false, "CDK不可用或次数已用完", null, false, cdk.getRemainingSpins(), request.getSessionId(), null
            );
        }

        // 使用一次CDK
        cdk.useOnce();
        cdkRepository.save(cdk);

        // 🎯 修复：获取转盘奖品列表（与前端保持一致）
        List<LuckyWheelPrize> wheelPrizes = prizeRepository.findAvailablePrizes();

        // 执行抽奖逻辑
        LuckyWheelPrize wonPrize = performLottery(wheelPrizes);

        // 🎯 修复：计算中奖奖品在转盘中的索引位置
        Integer prizeIndex = null;
        if (wonPrize != null) {
            for (int i = 0; i < wheelPrizes.size(); i++) {
                if (wheelPrizes.get(i).getId().equals(wonPrize.getId())) {
                    prizeIndex = i;
                    break;
                }
            }
        }

        // 记录转盘结果
        LuckyWheelSpinResult result = new LuckyWheelSpinResult();
        result.setSessionId(request.getSessionId());
        result.setCdkCode(request.getCdkCode());
        result.setIpAddress(ipAddress);
        result.setUserAgent(userAgent);

        boolean isWinning = wonPrize != null;
        result.setIsWinningSpin(isWinning);

        if (isWinning) {
            result.setPrizeId(wonPrize.getId());
            result.setPrizeName(wonPrize.getName());

            // 减少奖品库存
            wonPrize.decreaseStock();
            prizeRepository.save(wonPrize);
        }

        spinResultRepository.save(result);

        log.info("转盘执行完成，CDK: {}, 是否中奖: {}, 奖品: {}, 索引: {}",
                request.getCdkCode(), isWinning, isWinning ? wonPrize.getName() : "无", prizeIndex);

        return new LuckyWheelDTO.SpinResponse(
                true,
                isWinning ? "恭喜中奖！" : "很遗憾，未中奖",
                isWinning ? convertToPrizeInfo(wonPrize) : null,
                isWinning,
                cdk.getRemainingSpins(),
                request.getSessionId(),
                prizeIndex
        );
    }
    
    /**
     * 获取游戏状态
     */
    public LuckyWheelDTO.GameStateResponse getGameState(String sessionId, String cdkCode) {
        LuckyWheelCDK cdk = cdkRepository.findByCdkCode(cdkCode)
                .orElseThrow(() -> new RuntimeException("CDK不存在"));
        
        List<LuckyWheelDTO.SpinResultInfo> spinHistory = spinResultRepository
                .findBySessionIdOrderBySpinTimeDesc(sessionId)
                .stream()
                .map(this::convertToSpinResultInfo)
                .collect(Collectors.toList());
        
        return new LuckyWheelDTO.GameStateResponse(
                sessionId,
                cdkCode,
                cdk.getRemainingSpins(),
                spinHistory,
                cdk.isAvailable()
        );
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 执行抽奖逻辑
     */
    private LuckyWheelPrize performLottery() {
        List<LuckyWheelPrize> availablePrizes = prizeRepository.findAvailablePrizes();
        return performLottery(availablePrizes);
    }

    /**
     * 执行抽奖逻辑（使用指定的奖品列表）
     */
    private LuckyWheelPrize performLottery(List<LuckyWheelPrize> availablePrizes) {
        if (availablePrizes.isEmpty()) {
            return null; // 没有可用奖品
        }

        // 计算总概率
        BigDecimal totalProbability = availablePrizes.stream()
                .map(LuckyWheelPrize::getProbability)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 生成随机数
        double randomValue = random.nextDouble();
        BigDecimal randomDecimal = BigDecimal.valueOf(randomValue);

        // 按概率选择奖品
        BigDecimal cumulativeProbability = BigDecimal.ZERO;
        for (LuckyWheelPrize prize : availablePrizes) {
            cumulativeProbability = cumulativeProbability.add(
                    prize.getProbability().divide(totalProbability, 6, RoundingMode.HALF_UP)
            );

            if (randomDecimal.compareTo(cumulativeProbability) <= 0) {
                return prize;
            }
        }

        return null; // 未中奖
    }
    
    /**
     * 生成唯一的CDK代码
     */
    private String generateUniqueCDKCode(String prefix) {
        String basePrefix = prefix != null ? prefix : "LW";
        String cdkCode;
        
        do {
            String randomPart = generateRandomString(8);
            cdkCode = basePrefix + randomPart;
        } while (cdkRepository.existsByCdkCode(cdkCode));
        
        return cdkCode;
    }
    
    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "LW_" + System.currentTimeMillis() + "_" + generateRandomString(6);
    }
    
    // ==================== 转换方法 ====================
    
    private LuckyWheelDTO.PrizeInfo convertToPrizeInfo(LuckyWheelPrize prize) {
        return new LuckyWheelDTO.PrizeInfo(
                prize.getId(),
                prize.getName(),
                prize.getDescription(),
                prize.getImagePath(),
                prize.getTotalQuantity(),
                prize.getRemainingQuantity(),
                prize.getProbability(),
                prize.getIsActive(),
                prize.getSortOrder(),
                prize.getCreatedAt(),
                prize.getUpdatedAt()
        );
    }
    
    /**
     * 删除CDK
     */
    @Transactional
    public boolean deleteCDK(Long cdkId) {
        Optional<LuckyWheelCDK> cdkOpt = cdkRepository.findById(cdkId);

        if (cdkOpt.isEmpty()) {
            return false;
        }

        LuckyWheelCDK cdk = cdkOpt.get();

        // 只允许删除剩余次数为0的CDK
        if (cdk.getRemainingSpins() > 0) {
            throw new RuntimeException("只能删除剩余次数为0的CDK");
        }

        try {
            // 先删除相关的转盘记录
            spinResultRepository.deleteByCdkCode(cdk.getCdkCode());

            // 再删除CDK
            cdkRepository.delete(cdk);
            return true;
        } catch (Exception e) {
            throw new RuntimeException("删除CDK失败: " + e.getMessage());
        }
    }

    private LuckyWheelDTO.CDKInfo convertToCDKInfo(LuckyWheelCDK cdk) {
        return new LuckyWheelDTO.CDKInfo(
                cdk.getId(),
                cdk.getCdkCode(),
                cdk.getMaxSpins(),
                cdk.getUsedSpins(),
                cdk.getRemainingSpins(),
                cdk.getStatus(),
                cdk.getExpireTime(),
                cdk.getDescription(),
                cdk.getCreatedBy(),
                cdk.getCreatedAt(),
                cdk.getFirstUsedAt(),
                cdk.getLastUsedAt()
        );
    }
    
    private LuckyWheelDTO.SpinResultInfo convertToSpinResultInfo(LuckyWheelSpinResult result) {
        LuckyWheelDTO.PrizeInfo prizeInfo = null;
        if (result.getPrize() != null) {
            prizeInfo = convertToPrizeInfo(result.getPrize());
        }
        
        return new LuckyWheelDTO.SpinResultInfo(
                result.getId(),
                result.getCdkCode(),
                prizeInfo,
                result.getIsWinningSpin(),
                result.getSpinTime()
        );
    }
}
