package com.game.lianliankan.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class LuckyWheelDTO {
    
    // 奖品相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizeInfo {
        private Long id;
        private String name;
        private String description;
        private String imagePath;
        private Integer totalQuantity;
        private Integer remainingQuantity;
        private BigDecimal probability;
        private Boolean isActive;
        private Integer sortOrder;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizeCreateRequest {
        private String name;
        private String description;
        private String imagePath;
        private Integer totalQuantity;
        private BigDecimal probability;
        private Integer sortOrder;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizeUpdateRequest {
        private Long id;
        private String name;
        private String description;
        private String imagePath;
        private Integer totalQuantity;
        private BigDecimal probability;
        private Boolean isActive;
        private Integer sortOrder;
    }
    
    // CDK相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CDKInfo {
        private Long id;
        private String cdkCode;
        private Integer maxSpins;
        private Integer usedSpins;
        private Integer remainingSpins;
        private String status;
        private LocalDateTime expireTime;
        private String description;
        private String createdBy;
        private LocalDateTime createdAt;
        private LocalDateTime firstUsedAt;
        private LocalDateTime lastUsedAt;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CDKCreateRequest {
        private Integer count; // 生成数量
        private Integer maxSpins;
        private LocalDateTime expireTime;
        private String description;
        private String prefix; // CDK前缀
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CDKValidateRequest {
        private String cdkCode;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CDKValidateResponse {
        private Boolean isValid;
        private String message;
        private Integer remainingSpins;
        private LocalDateTime expireTime;
        private String sessionId; // 游戏会话ID
    }
    
    // 转盘游戏相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpinRequest {
        private String sessionId;
        private String cdkCode;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpinResponse {
        private Boolean success;
        private String message;
        private PrizeInfo prize; // 中奖奖品，null表示未中奖
        private Boolean isWinning;
        private Integer remainingSpins;
        private String sessionId;
        private Integer prizeIndex; // 中奖奖品在转盘中的索引位置（从0开始）
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GameStateResponse {
        private String sessionId;
        private String cdkCode;
        private Integer remainingSpins;
        private List<SpinResultInfo> spinHistory;
        private Boolean canSpin;
    }
    
    // 转盘结果相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpinResultInfo {
        private Long id;
        private String cdkCode;
        private PrizeInfo prize;
        private Boolean isWinning;
        private LocalDateTime spinTime;
    }
    
    // 管理员认证相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdminLoginRequest {
        private String username;
        private String password;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdminLoginResponse {
        private Boolean success;
        private String message;
        private String token; // 简单的会话标识
        private String displayName;
    }
    
    // 统计分析相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DashboardStats {
        private Long totalSpins;
        private Long totalWinnings;
        private Long totalCDKs;
        private Long activeCDKs;
        private Long totalPrizes;
        private Long availablePrizes;
        private BigDecimal winningRate;
        private List<PrizeWinningStats> prizeStats;
        private List<DailyStats> dailyStats;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizeWinningStats {
        private Long prizeId;
        private String prizeName;
        private Long winCount;
        private Integer remainingQuantity;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyStats {
        private String date;
        private Long totalSpins;
        private Long winnings;
        private BigDecimal winningRate;
    }
    
    // 批量操作相关DTO
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchCDKResponse {
        private Integer successCount;
        private List<String> cdkCodes;
        private String message;
    }
}
