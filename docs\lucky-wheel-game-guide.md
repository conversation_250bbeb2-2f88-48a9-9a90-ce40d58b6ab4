# 小里幸运转盘游戏使用指南

## 概述

小里幸运转盘是一个基于CDK（激活码）的转盘抽奖游戏，包含完整的前端用户界面和后台管理系统。

## 功能特性

### 用户端功能
- **CDK验证**: 用户输入CDK代码开始游戏
- **转盘游戏**: 可视化的转盘界面，支持动画效果
- **奖品展示**: 中奖后显示奖品信息
- **历史记录**: 查看当前会话的转盘历史
- **响应式设计**: 支持手机和桌面端

### 管理员功能
- **管理员认证**: 简单的用户名密码登录
- **奖品管理**: 添加、编辑、删除奖品，设置概率和库存
- **CDK管理**: 批量生成CDK，设置使用次数和过期时间
- **数据统计**: 仪表板显示转盘统计、中奖率等数据
- **记录查看**: 查看所有转盘记录和中奖情况

## 技术架构

### 后端技术栈
- **Spring Boot 3.2.0**: 主框架
- **Spring Data JPA**: 数据访问层
- **MySQL**: 数据库
- **Maven**: 项目管理

### 前端技术栈
- **Vue 3**: 前端框架
- **Vue Router**: 路由管理
- **Pinia**: 状态管理
- **Axios**: HTTP客户端
- **Vite**: 构建工具

## 数据库设计

### 核心表结构

#### 奖品表 (lucky_wheel_prizes)
- `id`: 主键
- `name`: 奖品名称
- `description`: 奖品描述
- `image_path`: 奖品图片路径
- `total_quantity`: 总数量
- `remaining_quantity`: 剩余数量
- `probability`: 中奖概率 (0-1)
- `is_active`: 是否启用
- `sort_order`: 排序顺序

#### CDK表 (lucky_wheel_cdk)
- `id`: 主键
- `cdk_code`: CDK代码（唯一）
- `max_spins`: 最大使用次数
- `used_spins`: 已使用次数
- `status`: 状态 (ACTIVE/EXPIRED/DISABLED)
- `expire_time`: 过期时间
- `created_by`: 创建者

#### 转盘记录表 (lucky_wheel_spin_results)
- `id`: 主键
- `session_id`: 会话ID
- `cdk_code`: 使用的CDK
- `prize_id`: 中奖奖品ID
- `is_winning_spin`: 是否中奖
- `spin_time`: 转盘时间
- `ip_address`: 用户IP

#### 管理员表 (lucky_wheel_admin)
- `id`: 主键
- `username`: 用户名（唯一）
- `password`: 密码
- `display_name`: 显示名称
- `status`: 状态
- `last_login_time`: 最后登录时间

## API接口

### 用户端接口
- `POST /lucky-wheel/validate-cdk`: 验证CDK
- `GET /lucky-wheel/prizes/available`: 获取可用奖品
- `POST /lucky-wheel/spin`: 执行转盘
- `GET /lucky-wheel/game-state`: 获取游戏状态

### 管理员接口
- `POST /lucky-wheel/admin/login`: 管理员登录
- `GET /lucky-wheel/admin/dashboard`: 获取仪表板数据
- `GET /lucky-wheel/admin/prizes`: 获取所有奖品
- `POST /lucky-wheel/admin/prizes`: 创建奖品
- `PUT /lucky-wheel/admin/prizes`: 更新奖品
- `DELETE /lucky-wheel/admin/prizes/{id}`: 删除奖品
- `GET /lucky-wheel/admin/cdks`: 获取所有CDK
- `POST /lucky-wheel/admin/cdks/generate`: 批量生成CDK
- `GET /lucky-wheel/admin/spin-results`: 获取转盘记录

## 部署说明

### 后端部署
1. 确保Java 17+环境
2. 配置MySQL数据库连接
3. 运行 `mvn clean package`
4. 启动 `java -jar target/lianliankan-backend-1.0.0.jar`

### 前端部署
1. 安装依赖: `npm install`
2. 开发环境: `npm run dev`
3. 生产构建: `npm run build`

### 数据库配置
在 `application-dev.yml` 或 `application-prod.yml` 中配置数据库连接：

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
```

## 使用流程

### 管理员操作流程
1. 访问 `/lucky-wheel/admin/login` 登录管理后台
2. 在奖品管理中添加奖品，设置名称、数量、概率等
3. 在CDK管理中批量生成CDK代码
4. 将CDK分发给用户
5. 在仪表板中查看统计数据和转盘记录

### 用户游戏流程
1. 访问 `/lucky-wheel` 进入游戏页面
2. 输入有效的CDK代码
3. 点击转盘按钮进行抽奖
4. 查看中奖结果和剩余次数
5. 可查看历史转盘记录

## 默认配置

### 默认管理员账户
- 用户名: `admin`
- 密码: `admin123`

### CDK格式
- 默认前缀: `LW`
- 代码长度: 10位（前缀+8位随机字符）
- 示例: `LW12345678`

## 注意事项

1. **安全性**: 生产环境请修改默认管理员密码
2. **概率设置**: 奖品概率总和建议不超过1.0
3. **库存管理**: 奖品库存为0时自动从转盘中移除
4. **CDK管理**: 过期或用完的CDK无法继续使用
5. **数据备份**: 定期备份数据库，特别是转盘记录

## 扩展功能

可以考虑添加的功能：
- 奖品图片上传功能
- 更复杂的概率算法
- 用户注册和登录系统
- 奖品兑换功能
- 微信/支付宝集成
- 数据导出功能

## 故障排除

### 常见问题
1. **CDK验证失败**: 检查CDK是否正确、是否过期、是否还有剩余次数
2. **转盘无法启动**: 确认后端服务正常运行，检查网络连接
3. **管理员无法登录**: 确认用户名密码正确，检查数据库连接
4. **奖品不显示**: 检查奖品是否启用，库存是否充足

### 日志查看
- 后端日志位置: `backend/logs/lianliankan-backend.log`
- 前端控制台: 浏览器开发者工具Console面板

## 联系支持

如有问题或建议，请联系开发团队。
