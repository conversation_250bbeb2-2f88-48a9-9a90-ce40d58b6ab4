package com.game.lianliankan.repository;

import com.game.lianliankan.entity.LuckyWheelCDK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LuckyWheelCDKRepository extends JpaRepository<LuckyWheelCDK, Long> {
    
    /**
     * 根据CDK代码查找
     */
    Optional<LuckyWheelCDK> findByCdkCode(String cdkCode);
    
    /**
     * 查找所有激活状态的CDK
     */
    List<LuckyWheelCDK> findByStatusOrderByCreatedAtDesc(String status);
    
    /**
     * 查找所有CDK，按创建时间倒序
     */
    List<LuckyWheelCDK> findAllByOrderByCreatedAtDesc();
    
    /**
     * 查找指定时间范围内创建的CDK
     */
    List<LuckyWheelCDK> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找已使用的CDK
     */
    @Query("SELECT c FROM LuckyWheelCDK c WHERE c.usedSpins > 0 ORDER BY c.lastUsedAt DESC")
    List<LuckyWheelCDK> findUsedCDKs();
    
    /**
     * 查找未使用的CDK
     */
    @Query("SELECT c FROM LuckyWheelCDK c WHERE c.usedSpins = 0 ORDER BY c.createdAt DESC")
    List<LuckyWheelCDK> findUnusedCDKs();
    
    /**
     * 统计CDK使用情况
     */
    @Query("SELECT COUNT(c) FROM LuckyWheelCDK c WHERE c.status = :status")
    long countByStatus(@Param("status") String status);
    
    /**
     * 统计总使用次数
     */
    @Query("SELECT COALESCE(SUM(c.usedSpins), 0) FROM LuckyWheelCDK c")
    long getTotalUsedSpins();
    
    /**
     * 检查CDK代码是否存在
     */
    boolean existsByCdkCode(String cdkCode);
}
