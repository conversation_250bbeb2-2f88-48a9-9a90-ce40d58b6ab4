package com.game.lianliankan.repository;

import com.game.lianliankan.entity.LuckyWheelPrize;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LuckyWheelPrizeRepository extends JpaRepository<LuckyWheelPrize, Long> {
    
    /**
     * 查找所有激活的奖品，按排序顺序
     */
    List<LuckyWheelPrize> findByIsActiveTrueOrderBySortOrderAsc();
    
    /**
     * 查找所有有库存且激活的奖品
     */
    @Query("SELECT p FROM LuckyWheelPrize p WHERE p.isActive = true AND p.remainingQuantity > 0 ORDER BY p.sortOrder ASC")
    List<LuckyWheelPrize> findAvailablePrizes();
    
    /**
     * 查找所有奖品，按排序顺序
     */
    List<LuckyWheelPrize> findAllByOrderBySortOrderAsc();
    
    /**
     * 统计激活的奖品数量
     */
    long countByIsActiveTrue();
    
    /**
     * 统计有库存的奖品数量
     */
    @Query("SELECT COUNT(p) FROM LuckyWheelPrize p WHERE p.isActive = true AND p.remainingQuantity > 0")
    long countAvailablePrizes();
}
