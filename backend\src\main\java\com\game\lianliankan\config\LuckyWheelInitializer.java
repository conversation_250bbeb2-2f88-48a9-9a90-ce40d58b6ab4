package com.game.lianliankan.config;

import com.game.lianliankan.service.LuckyWheelAdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class LuckyWheelInitializer implements CommandLineRunner {
    
    private final LuckyWheelAdminService adminService;
    
    @Override
    public void run(String... args) throws Exception {
        try {
            // 初始化默认管理员账户
            adminService.initDefaultAdmin();
            log.info("幸运转盘模块初始化完成");
        } catch (Exception e) {
            log.error("幸运转盘模块初始化失败", e);
        }
    }
}
