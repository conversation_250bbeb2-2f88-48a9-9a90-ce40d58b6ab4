package com.game.lianliankan.repository;

import com.game.lianliankan.entity.LuckyWheelSpinResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface LuckyWheelSpinResultRepository extends JpaRepository<LuckyWheelSpinResult, Long> {
    
    /**
     * 根据CDK代码查找转盘结果
     */
    List<LuckyWheelSpinResult> findByCdkCodeOrderBySpinTimeDesc(String cdkCode);
    
    /**
     * 根据会话ID查找转盘结果
     */
    List<LuckyWheelSpinResult> findBySessionIdOrderBySpinTimeDesc(String sessionId);
    
    /**
     * 查找所有转盘结果，按时间倒序
     */
    List<LuckyWheelSpinResult> findAllByOrderBySpinTimeDesc();
    
    /**
     * 查找指定时间范围内的转盘结果
     */
    List<LuckyWheelSpinResult> findBySpinTimeBetweenOrderBySpinTimeDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找中奖记录
     */
    List<LuckyWheelSpinResult> findByIsWinningSpinTrueOrderBySpinTimeDesc();
    
    /**
     * 查找指定奖品的中奖记录
     */
    List<LuckyWheelSpinResult> findByPrizeIdAndIsWinningSpinTrueOrderBySpinTimeDesc(Long prizeId);
    
    /**
     * 统计总转盘次数
     */
    long count();
    
    /**
     * 统计中奖次数
     */
    long countByIsWinningSpinTrue();
    
    /**
     * 统计指定CDK的转盘次数
     */
    long countByCdkCode(String cdkCode);
    
    /**
     * 统计指定时间范围内的转盘次数
     */
    long countBySpinTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的中奖次数
     */
    long countBySpinTimeBetweenAndIsWinningSpinTrue(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定奖品的中奖次数
     */
    long countByPrizeIdAndIsWinningSpinTrue(Long prizeId);
    
    /**
     * 获取奖品中奖统计
     */
    @Query("SELECT p.id, p.name, COUNT(r) FROM LuckyWheelSpinResult r " +
           "JOIN r.prize p WHERE r.isWinningSpin = true " +
           "GROUP BY p.id, p.name ORDER BY COUNT(r) DESC")
    List<Object[]> getPrizeWinningStats();
    
    /**
     * 获取CDK使用统计
     */
    @Query("SELECT r.cdkCode, COUNT(r) FROM LuckyWheelSpinResult r " +
           "GROUP BY r.cdkCode ORDER BY COUNT(r) DESC")
    List<Object[]> getCDKUsageStats();
    
    /**
     * 获取每日转盘统计
     */
    @Query("SELECT DATE(r.spinTime), COUNT(r), COUNT(CASE WHEN r.isWinningSpin = true THEN 1 END) " +
           "FROM LuckyWheelSpinResult r " +
           "WHERE r.spinTime >= :startDate " +
           "GROUP BY DATE(r.spinTime) ORDER BY DATE(r.spinTime) DESC")
    List<Object[]> getDailySpinStats(@Param("startDate") LocalDateTime startDate);

    /**
     * 根据CDK代码删除转盘记录
     */
    @Modifying
    void deleteByCdkCode(String cdkCode);
}
