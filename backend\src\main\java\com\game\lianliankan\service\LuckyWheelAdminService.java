package com.game.lianliankan.service;

import com.game.lianliankan.dto.LuckyWheelDTO;
import com.game.lianliankan.entity.LuckyWheelAdmin;
import com.game.lianliankan.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class LuckyWheelAdminService {
    
    private final LuckyWheelAdminRepository adminRepository;
    private final LuckyWheelPrizeRepository prizeRepository;
    private final LuckyWheelCDKRepository cdkRepository;
    private final LuckyWheelSpinResultRepository spinResultRepository;
    
    // 简单的会话管理（生产环境建议使用Redis或JWT）
    private final Map<String, String> activeSessions = new HashMap<>();
    
    /**
     * 管理员登录
     */
    @Transactional
    public LuckyWheelDTO.AdminLoginResponse login(LuckyWheelDTO.AdminLoginRequest request, String ipAddress) {
        Optional<LuckyWheelAdmin> adminOpt = adminRepository.findByUsername(request.getUsername());
        
        if (adminOpt.isEmpty()) {
            log.warn("管理员登录失败：用户名不存在 - {}", request.getUsername());
            return new LuckyWheelDTO.AdminLoginResponse(false, "用户名或密码错误", null, null);
        }
        
        LuckyWheelAdmin admin = adminOpt.get();
        
        if (!admin.isActive()) {
            log.warn("管理员登录失败：账户已禁用 - {}", request.getUsername());
            return new LuckyWheelDTO.AdminLoginResponse(false, "账户已被禁用", null, null);
        }
        
        // 简单的密码验证（生产环境应使用BCrypt等加密方式）
        if (!admin.getPassword().equals(request.getPassword())) {
            log.warn("管理员登录失败：密码错误 - {}", request.getUsername());
            return new LuckyWheelDTO.AdminLoginResponse(false, "用户名或密码错误", null, null);
        }
        
        // 记录登录信息
        admin.recordLogin(ipAddress);
        adminRepository.save(admin);
        
        // 生成会话令牌
        String token = generateSessionToken();
        activeSessions.put(token, admin.getUsername());
        
        log.info("管理员登录成功 - {}", request.getUsername());
        
        return new LuckyWheelDTO.AdminLoginResponse(
                true, 
                "登录成功", 
                token, 
                admin.getDisplayName() != null ? admin.getDisplayName() : admin.getUsername()
        );
    }
    
    /**
     * 验证会话
     */
    public boolean validateSession(String token) {
        return token != null && activeSessions.containsKey(token);
    }
    
    /**
     * 获取当前登录用户
     */
    public String getCurrentUser(String token) {
        return activeSessions.get(token);
    }
    
    /**
     * 登出
     */
    public void logout(String token) {
        activeSessions.remove(token);
    }
    
    /**
     * 获取仪表板统计数据
     */
    public LuckyWheelDTO.DashboardStats getDashboardStats() {
        // 基础统计
        long totalSpins = spinResultRepository.count();
        long totalWinnings = spinResultRepository.countByIsWinningSpinTrue();
        long totalCDKs = cdkRepository.count();
        long activeCDKs = cdkRepository.countByStatus("ACTIVE");
        long totalPrizes = prizeRepository.count();
        long availablePrizes = prizeRepository.countAvailablePrizes();
        
        // 计算中奖率
        BigDecimal winningRate = BigDecimal.ZERO;
        if (totalSpins > 0) {
            winningRate = BigDecimal.valueOf(totalWinnings)
                    .divide(BigDecimal.valueOf(totalSpins), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        
        // 奖品中奖统计
        List<LuckyWheelDTO.PrizeWinningStats> prizeStats = getPrizeWinningStats();
        
        // 每日统计（最近7天）
        List<LuckyWheelDTO.DailyStats> dailyStats = getDailyStats(7);
        
        return new LuckyWheelDTO.DashboardStats(
                totalSpins,
                totalWinnings,
                totalCDKs,
                activeCDKs,
                totalPrizes,
                availablePrizes,
                winningRate,
                prizeStats,
                dailyStats
        );
    }
    
    /**
     * 获取奖品中奖统计
     */
    private List<LuckyWheelDTO.PrizeWinningStats> getPrizeWinningStats() {
        List<Object[]> results = spinResultRepository.getPrizeWinningStats();
        
        return results.stream()
                .map(row -> {
                    Long prizeId = (Long) row[0];
                    String prizeName = (String) row[1];
                    Long winCount = (Long) row[2];
                    
                    // 获取剩余数量
                    Integer remainingQuantity = prizeRepository.findById(prizeId)
                            .map(prize -> prize.getRemainingQuantity())
                            .orElse(0);
                    
                    return new LuckyWheelDTO.PrizeWinningStats(prizeId, prizeName, winCount, remainingQuantity);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取每日统计
     */
    private List<LuckyWheelDTO.DailyStats> getDailyStats(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        List<Object[]> results = spinResultRepository.getDailySpinStats(startDate);
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        return results.stream()
                .map(row -> {
                    String date = row[0].toString();
                    Long totalSpins = (Long) row[1];
                    Long winnings = (Long) row[2];
                    
                    BigDecimal winningRate = BigDecimal.ZERO;
                    if (totalSpins > 0) {
                        winningRate = BigDecimal.valueOf(winnings)
                                .divide(BigDecimal.valueOf(totalSpins), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100));
                    }
                    
                    return new LuckyWheelDTO.DailyStats(date, totalSpins, winnings, winningRate);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有转盘记录
     */
    public List<LuckyWheelDTO.SpinResultInfo> getAllSpinResults() {
        return spinResultRepository.findAllByOrderBySpinTimeDesc()
                .stream()
                .map(this::convertToSpinResultInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定时间范围的转盘记录
     */
    public List<LuckyWheelDTO.SpinResultInfo> getSpinResultsByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        return spinResultRepository.findBySpinTimeBetweenOrderBySpinTimeDesc(startTime, endTime)
                .stream()
                .map(this::convertToSpinResultInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * 初始化默认管理员账户
     */
    @Transactional
    public void initDefaultAdmin() {
        if (adminRepository.count() == 0) {
            LuckyWheelAdmin admin = new LuckyWheelAdmin();
            admin.setUsername("admin");
            admin.setPassword("admin123"); // 生产环境应使用加密密码
            admin.setDisplayName("系统管理员");
            admin.setStatus("ACTIVE");
            
            adminRepository.save(admin);
            log.info("初始化默认管理员账户成功");
        }
    }
    
    /**
     * 生成会话令牌
     */
    private String generateSessionToken() {
        return "LW_ADMIN_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 转换转盘结果
     */
    private LuckyWheelDTO.SpinResultInfo convertToSpinResultInfo(com.game.lianliankan.entity.LuckyWheelSpinResult result) {
        LuckyWheelDTO.PrizeInfo prizeInfo = null;
        if (result.getPrize() != null) {
            prizeInfo = new LuckyWheelDTO.PrizeInfo(
                    result.getPrize().getId(),
                    result.getPrize().getName(),
                    result.getPrize().getDescription(),
                    result.getPrize().getImagePath(),
                    result.getPrize().getTotalQuantity(),
                    result.getPrize().getRemainingQuantity(),
                    result.getPrize().getProbability(),
                    result.getPrize().getIsActive(),
                    result.getPrize().getSortOrder(),
                    result.getPrize().getCreatedAt(),
                    result.getPrize().getUpdatedAt()
            );
        }
        
        return new LuckyWheelDTO.SpinResultInfo(
                result.getId(),
                result.getCdkCode(),
                prizeInfo,
                result.getIsWinningSpin(),
                result.getSpinTime()
        );
    }
}
