import{c as n,t as o}from"./index-16760d9c.js";function t(r){return`${window.location.origin}/?share=${encodeURIComponent(r)}`}function c(){const e=new URLSearchParams(window.location.search).get("share");return e?decodeURIComponent(e):null}async function i(r){try{const e=t(r),a=await n(e);return a?o.success(`分享链接已复制到剪贴板！
其他人点击链接即可加载您的游戏进度`,{duration:3e3}):o.error("复制失败，请手动复制链接",{duration:3e3}),a}catch(e){return console.error("分享失败:",e),o.error("分享功能暂时不可用",{duration:3e3}),!1}}function l(){const r=new URL(window.location);r.searchParams.delete("share"),window.history.replaceState({},"",r.toString())}export{l as clearShareParamFromUrl,t as generateShareLink,c as getSharedProgressFromUrl,i as shareGameProgress};
