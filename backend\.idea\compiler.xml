<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="lianliankan-backend" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="luckywheel-backend" target="21" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="lianliankan-backend" options="-parameters" />
      <module name="luckywheel-backend" options="-parameters" />
    </option>
  </component>
</project>