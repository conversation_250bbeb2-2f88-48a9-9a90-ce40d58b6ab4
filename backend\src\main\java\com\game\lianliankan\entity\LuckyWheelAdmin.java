package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "lucky_wheel_admin",
       indexes = {
           @Index(name = "idx_admin_username", columnList = "username", unique = true),
           @Index(name = "idx_admin_status", columnList = "status")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyWheelAdmin {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "username", nullable = false, unique = true, length = 50)
    private String username;
    
    @Column(name = "password", nullable = false, length = 255)
    private String password; // 存储加密后的密码
    
    @Column(name = "display_name", length = 100)
    private String displayName;
    
    @Column(name = "status", length = 20)
    private String status = "ACTIVE"; // ACTIVE, DISABLED
    
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;
    
    @Column(name = "last_login_ip", length = 45)
    private String lastLoginIp;
    
    @Column(name = "login_count")
    private Integer loginCount = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查管理员是否可用
     */
    public boolean isActive() {
        return "ACTIVE".equals(status);
    }
    
    /**
     * 记录登录
     */
    public void recordLogin(String ipAddress) {
        lastLoginTime = LocalDateTime.now();
        lastLoginIp = ipAddress;
        loginCount++;
    }
}
