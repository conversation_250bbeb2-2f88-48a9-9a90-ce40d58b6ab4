package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "lucky_wheel_spin_results",
       indexes = {
           @Index(name = "idx_spin_cdk", columnList = "cdk_code"),
           @Index(name = "idx_spin_prize", columnList = "prize_id"),
           @Index(name = "idx_spin_time", columnList = "spin_time"),
           @Index(name = "idx_spin_session", columnList = "session_id")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyWheelSpinResult {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "session_id", length = 100)
    private String sessionId;
    
    @Column(name = "cdk_code", nullable = false, length = 50)
    private String cdkCode;
    
    @Column(name = "prize_id")
    private Long prizeId;
    
    @Column(name = "prize_name", length = 100)
    private String prizeName;
    
    @Column(name = "is_winning_spin")
    private Boolean isWinningSpin = false;
    
    @Column(name = "spin_time")
    private LocalDateTime spinTime;
    
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cdk_code", referencedColumnName = "cdk_code", insertable = false, updatable = false)
    private LuckyWheelCDK cdk;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prize_id", insertable = false, updatable = false)
    private LuckyWheelPrize prize;
    
    @PrePersist
    protected void onCreate() {
        spinTime = LocalDateTime.now();
    }
}
