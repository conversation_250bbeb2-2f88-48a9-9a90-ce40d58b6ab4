package com.game.lianliankan.controller;

import com.game.lianliankan.dto.ApiResponse;
import com.game.lianliankan.dto.LuckyWheelDTO;
import com.game.lianliankan.service.LuckyWheelAdminService;
import com.game.lianliankan.service.LuckyWheelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/lucky-wheel/admin")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class LuckyWheelAdminController {
    
    private final LuckyWheelAdminService adminService;
    private final LuckyWheelService luckyWheelService;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public ApiResponse<LuckyWheelDTO.AdminLoginResponse> login(@RequestBody LuckyWheelDTO.AdminLoginRequest request, HttpServletRequest httpRequest) {
        try {
            String ipAddress = getClientIpAddress(httpRequest);
            LuckyWheelDTO.AdminLoginResponse response = adminService.login(request, ipAddress);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout(@RequestHeader("Authorization") String token) {
        try {
            adminService.logout(token);
            return ApiResponse.success("登出成功", null);
        } catch (Exception e) {
            log.error("管理员登出失败", e);
            return ApiResponse.error("登出失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取仪表板统计数据
     */
    @GetMapping("/dashboard")
    public ApiResponse<LuckyWheelDTO.DashboardStats> getDashboard(@RequestHeader("Authorization") String token) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            LuckyWheelDTO.DashboardStats stats = adminService.getDashboardStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取仪表板数据失败", e);
            return ApiResponse.error("获取仪表板数据失败: " + e.getMessage());
        }
    }
    
    // ==================== 奖品管理 ====================
    
    /**
     * 获取所有奖品
     */
    @GetMapping("/prizes")
    public ApiResponse<List<LuckyWheelDTO.PrizeInfo>> getAllPrizes(@RequestHeader("Authorization") String token) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            List<LuckyWheelDTO.PrizeInfo> prizes = luckyWheelService.getAllPrizes();
            return ApiResponse.success(prizes);
        } catch (Exception e) {
            log.error("获取奖品列表失败", e);
            return ApiResponse.error("获取奖品列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建奖品
     */
    @PostMapping("/prizes")
    public ApiResponse<LuckyWheelDTO.PrizeInfo> createPrize(
            @RequestHeader("Authorization") String token,
            @RequestBody LuckyWheelDTO.PrizeCreateRequest request) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            LuckyWheelDTO.PrizeInfo prize = luckyWheelService.createPrize(request);
            return ApiResponse.success("创建奖品成功", prize);
        } catch (Exception e) {
            log.error("创建奖品失败", e);
            return ApiResponse.error("创建奖品失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新奖品
     */
    @PutMapping("/prizes")
    public ApiResponse<LuckyWheelDTO.PrizeInfo> updatePrize(
            @RequestHeader("Authorization") String token,
            @RequestBody LuckyWheelDTO.PrizeUpdateRequest request) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            LuckyWheelDTO.PrizeInfo prize = luckyWheelService.updatePrize(request);
            return ApiResponse.success("更新奖品成功", prize);
        } catch (Exception e) {
            log.error("更新奖品失败", e);
            return ApiResponse.error("更新奖品失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除奖品
     */
    @DeleteMapping("/prizes/{prizeId}")
    public ApiResponse<Void> deletePrize(
            @RequestHeader("Authorization") String token,
            @PathVariable Long prizeId) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            luckyWheelService.deletePrize(prizeId);
            return ApiResponse.success("删除奖品成功", null);
        } catch (Exception e) {
            log.error("删除奖品失败", e);
            return ApiResponse.error("删除奖品失败: " + e.getMessage());
        }
    }
    
    // ==================== CDK管理 ====================
    
    /**
     * 获取所有CDK
     */
    @GetMapping("/cdks")
    public ApiResponse<List<LuckyWheelDTO.CDKInfo>> getAllCDKs(@RequestHeader("Authorization") String token) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            List<LuckyWheelDTO.CDKInfo> cdks = luckyWheelService.getAllCDKs();
            return ApiResponse.success(cdks);
        } catch (Exception e) {
            log.error("获取CDK列表失败", e);
            return ApiResponse.error("获取CDK列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量生成CDK
     */
    @PostMapping("/cdks/generate")
    public ApiResponse<LuckyWheelDTO.BatchCDKResponse> generateCDKs(
            @RequestHeader("Authorization") String token,
            @RequestBody LuckyWheelDTO.CDKCreateRequest request) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }

            String currentUser = adminService.getCurrentUser(token);
            LuckyWheelDTO.BatchCDKResponse response = luckyWheelService.generateCDKs(request, currentUser);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("生成CDK失败", e);
            return ApiResponse.error("生成CDK失败: " + e.getMessage());
        }
    }

    /**
     * 删除CDK
     */
    @DeleteMapping("/cdks/{cdkId}")
    public ApiResponse<String> deleteCDK(
            @RequestHeader("Authorization") String token,
            @PathVariable Long cdkId) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }

            boolean deleted = luckyWheelService.deleteCDK(cdkId);
            if (deleted) {
                return ApiResponse.success("CDK删除成功");
            } else {
                return ApiResponse.error("CDK不存在或删除失败");
            }
        } catch (Exception e) {
            log.error("删除CDK失败", e);
            return ApiResponse.error("删除CDK失败: " + e.getMessage());
        }
    }
    
    // ==================== 转盘记录管理 ====================
    
    /**
     * 获取所有转盘记录
     */
    @GetMapping("/spin-results")
    public ApiResponse<List<LuckyWheelDTO.SpinResultInfo>> getAllSpinResults(@RequestHeader("Authorization") String token) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            List<LuckyWheelDTO.SpinResultInfo> results = adminService.getAllSpinResults();
            return ApiResponse.success(results);
        } catch (Exception e) {
            log.error("获取转盘记录失败", e);
            return ApiResponse.error("获取转盘记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据日期范围获取转盘记录
     */
    @GetMapping("/spin-results/date-range")
    public ApiResponse<List<LuckyWheelDTO.SpinResultInfo>> getSpinResultsByDateRange(
            @RequestHeader("Authorization") String token,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            if (!adminService.validateSession(token)) {
                return ApiResponse.error(401, "未授权访问");
            }
            
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = LocalDateTime.parse(startDate + " 00:00:00", formatter);
            LocalDateTime endTime = LocalDateTime.parse(endDate + " 23:59:59", formatter);
            
            List<LuckyWheelDTO.SpinResultInfo> results = adminService.getSpinResultsByDateRange(startTime, endTime);
            return ApiResponse.success(results);
        } catch (Exception e) {
            log.error("根据日期范围获取转盘记录失败", e);
            return ApiResponse.error("获取转盘记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
