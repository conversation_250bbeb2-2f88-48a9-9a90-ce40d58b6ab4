# 物品选择游戏使用指南

## 游戏简介

物品选择游戏是一个简单而有趣的选择类游戏，玩家可以在网格中选择物品，并通过确认、重置、导出、导入等功能管理游戏状态。

## 快速开始

1. 启动后端服务（需要Java 21+）
2. 启动前端服务
3. 从主页面创建新游戏
4. 支持数据持久化和导入导出功能

## 游戏特色

- **直观的界面**：8x8网格布局，每个物品都有对应的图片和名称
- **三种状态**：
  - 默认状态：正常显示
  - 待确认状态：绿色背景 + "待确认"标签
  - 已确认状态：黄色背景
- **四个功能按钮**：确认、重置、导出、导入
- **数据持久化**：支持保存到数据库和导入导出功能

## 游戏玩法

### 1. 开始游戏
1. 在主页面点击"物品选择"游戏卡片
2. 输入玩家名称
3. 点击"开始游戏"

### 2. 选择物品
- 点击任意物品，物品会变为绿色背景并显示"待确认"标签
- 再次点击可以取消选择
- 已确认的物品（黄色背景）无法再次点击

### 3. 功能按钮

#### 确认按钮
- 将所有待确认（绿色）的物品变为已确认（黄色）状态
- 按钮显示当前待确认物品数量
- 如果没有待确认物品，按钮会被禁用

#### 重置按钮
- 清除所有选择状态，将所有物品恢复为默认状态
- 会弹出确认对话框防止误操作

#### 导出按钮
- 将当前游戏状态导出为代码
- 导出代码会自动复制到剪贴板
- 可以分享给其他人或保存备份

#### 导入按钮
- 通过导入代码恢复游戏状态
- 支持导入其他玩家分享的游戏状态
- 导入后会覆盖当前游戏状态

## 技术实现

### 前端技术栈
- Vue 3 + Composition API
- CSS Grid 响应式布局
- Axios HTTP 客户端

### 后端技术栈
- Spring Boot
- JPA/Hibernate
- MySQL 数据库
- RESTful API

### 数据库设计
- `game_types` 表：存储游戏类型信息
- `item_selection_items` 表：存储物品信息
- `game_sessions` 表：存储游戏会话和状态

## API 接口

### 创建游戏会话
```
POST /api/item-selection/create
{
  "playerName": "玩家名称"
}
```

### 获取游戏状态
```
GET /api/item-selection/{sessionId}
```

### 更新游戏状态
```
POST /api/item-selection/update
{
  "sessionId": "会话ID",
  "items": [物品列表]
}
```

### 确认选择
```
POST /api/item-selection/confirm
{
  "sessionId": "会话ID"
}
```

### 重置游戏
```
POST /api/item-selection/reset
{
  "sessionId": "会话ID"
}
```

### 导出游戏状态
```
POST /api/item-selection/export
{
  "sessionId": "会话ID"
}
```

### 导入游戏状态
```
POST /api/item-selection/import
{
  "sessionId": "会话ID",
  "importCode": "导入代码"
}
```

## 部署说明

### 数据库初始化
1. 执行 `backend/src/main/resources/db/lianliankan.sql` 创建基础表结构
2. 执行 `backend/src/main/resources/db/item_selection_game.sql` 添加物品选择游戏数据

### 启动后端
```bash
cd backend
mvn spring-boot:run
```

### 启动前端
```bash
cd frontend
npm run dev
```

## 测试模式

当后端服务不可用时，前端会自动进入测试模式：
- 使用模拟数据展示游戏界面
- 所有功能都可以正常使用（除了数据持久化）
- 导出导入功能使用本地存储

## 注意事项

1. 确保所有物品图片文件存在于 `frontend/public/images/` 目录中
2. 游戏状态会自动保存到数据库
3. 导出代码使用 Base64 编码，包含完整的游戏状态信息
4. 建议定期备份重要的游戏状态

## 未来扩展

- 添加更多物品类型
- 支持自定义网格大小
- 添加游戏统计功能
- 支持多人协作模式
- 添加物品分类和筛选功能
