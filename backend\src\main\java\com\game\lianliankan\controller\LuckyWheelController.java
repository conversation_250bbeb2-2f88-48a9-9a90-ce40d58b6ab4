package com.game.lianliankan.controller;

import com.game.lianliankan.dto.ApiResponse;
import com.game.lianliankan.dto.LuckyWheelDTO;
import com.game.lianliankan.service.LuckyWheelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/lucky-wheel")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class LuckyWheelController {
    
    private final LuckyWheelService luckyWheelService;
    
    /**
     * 验证CDK
     */
    @PostMapping("/validate-cdk")
    public ApiResponse<LuckyWheelDTO.CDKValidateResponse> validateCDK(@RequestBody LuckyWheelDTO.CDKValidateRequest request) {
        try {
            LuckyWheelDTO.CDKValidateResponse response = luckyWheelService.validateCDK(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("验证CDK失败", e);
            return ApiResponse.error("验证CDK失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可用奖品列表（用于转盘显示）
     */
    @GetMapping("/prizes/available")
    public ApiResponse<List<LuckyWheelDTO.PrizeInfo>> getAvailablePrizes() {
        try {
            List<LuckyWheelDTO.PrizeInfo> prizes = luckyWheelService.getAvailablePrizes();
            return ApiResponse.success(prizes);
        } catch (Exception e) {
            log.error("获取可用奖品失败", e);
            return ApiResponse.error("获取可用奖品失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行转盘
     */
    @PostMapping("/spin")
    public ApiResponse<LuckyWheelDTO.SpinResponse> spin(@RequestBody LuckyWheelDTO.SpinRequest request, HttpServletRequest httpRequest) {
        try {
            String ipAddress = getClientIpAddress(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            LuckyWheelDTO.SpinResponse response = luckyWheelService.spin(request, ipAddress, userAgent);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("转盘执行失败", e);
            return ApiResponse.error("转盘执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取游戏状态
     */
    @GetMapping("/game-state")
    public ApiResponse<LuckyWheelDTO.GameStateResponse> getGameState(
            @RequestParam String sessionId, 
            @RequestParam String cdkCode) {
        try {
            LuckyWheelDTO.GameStateResponse response = luckyWheelService.getGameState(sessionId, cdkCode);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取游戏状态失败", e);
            return ApiResponse.error("获取游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
