-- 幸运转盘游戏表结构（仅表结构，用于现有数据库）

-- 创建奖品表
CREATE TABLE IF NOT EXISTS lucky_wheel_prizes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '奖品名称',
    description TEXT COMMENT '奖品描述',
    image_path VARCHAR(200) COMMENT '奖品图片路径',
    total_quantity INT NOT NULL COMMENT '总数量',
    remaining_quantity INT NOT NULL COMMENT '剩余数量',
    probability DECIMAL(5,4) NOT NULL COMMENT '中奖概率',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_prize_active (is_active),
    INDEX idx_prize_probability (probability)
) COMMENT '幸运转盘奖品表';

-- 创建CDK表
CREATE TABLE IF NOT EXISTS lucky_wheel_cdk (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cdk_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'CDK代码',
    max_spins INT NOT NULL COMMENT '最大使用次数',
    used_spins INT DEFAULT 0 COMMENT '已使用次数',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    expire_time DATETIME COMMENT '过期时间',
    description VARCHAR(200) COMMENT '描述',
    created_by VARCHAR(50) COMMENT '创建者',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    first_used_at DATETIME COMMENT '首次使用时间',
    last_used_at DATETIME COMMENT '最后使用时间',
    UNIQUE INDEX idx_cdk_code (cdk_code),
    INDEX idx_cdk_status (status),
    INDEX idx_cdk_created (created_at)
) COMMENT '幸运转盘CDK表';

-- 创建转盘记录表
CREATE TABLE IF NOT EXISTS lucky_wheel_spin_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) COMMENT '会话ID',
    cdk_code VARCHAR(50) NOT NULL COMMENT 'CDK代码',
    prize_id BIGINT COMMENT '奖品ID',
    prize_name VARCHAR(100) COMMENT '奖品名称',
    is_winning_spin BOOLEAN DEFAULT FALSE COMMENT '是否中奖',
    spin_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '转盘时间',
    user_agent VARCHAR(500) COMMENT '用户代理',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    INDEX idx_spin_cdk (cdk_code),
    INDEX idx_spin_prize (prize_id),
    INDEX idx_spin_time (spin_time),
    INDEX idx_spin_session (session_id),
    FOREIGN KEY (prize_id) REFERENCES lucky_wheel_prizes(id) ON DELETE SET NULL
) COMMENT '幸运转盘记录表';

-- 创建管理员表
CREATE TABLE IF NOT EXISTS lucky_wheel_admin (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    display_name VARCHAR(100) COMMENT '显示名称',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_admin_username (username),
    INDEX idx_admin_status (status)
) COMMENT '幸运转盘管理员表';

-- 插入默认管理员账户
INSERT IGNORE INTO lucky_wheel_admin (username, password, display_name, status) 
VALUES ('admin', 'admin123', '系统管理员', 'ACTIVE');

-- 插入示例奖品数据
INSERT IGNORE INTO lucky_wheel_prizes (id, name, description, image_path, total_quantity, remaining_quantity, probability, is_active, sort_order) VALUES
(1, '一等奖', '恭喜获得一等奖！价值1000元奖品', '/images/prize1.png', 5, 5, 0.02, TRUE, 1),
(2, '二等奖', '恭喜获得二等奖！价值500元奖品', '/images/prize2.png', 10, 10, 0.05, TRUE, 2),
(3, '三等奖', '恭喜获得三等奖！价值200元奖品', '/images/prize3.png', 20, 20, 0.10, TRUE, 3),
(4, '四等奖', '恭喜获得四等奖！价值100元奖品', '/images/prize4.png', 50, 50, 0.15, TRUE, 4),
(5, '五等奖', '恭喜获得五等奖！价值50元奖品', '/images/prize5.png', 100, 100, 0.20, TRUE, 5),
(6, '参与奖', '感谢参与！获得纪念品一份', '/images/prize6.png', 200, 200, 0.30, TRUE, 6);

-- 插入示例CDK数据
INSERT IGNORE INTO lucky_wheel_cdk (id, cdk_code, max_spins, used_spins, status, expire_time, description, created_by) VALUES
(1, 'LW12345678', 5, 0, 'ACTIVE', '2025-12-31 23:59:59', '测试CDK - 5次机会', 'admin'),
(2, 'LW87654321', 10, 0, 'ACTIVE', '2025-12-31 23:59:59', '测试CDK - 10次机会', 'admin'),
(3, 'LWABCDEFGH', 3, 0, 'ACTIVE', '2025-12-31 23:59:59', '测试CDK - 3次机会', 'admin'),
(4, 'LWTESTCODE', 1, 0, 'ACTIVE', '2025-12-31 23:59:59', '测试CDK - 1次机会', 'admin'),
(5, 'LWDEMOCODE', 20, 0, 'ACTIVE', '2025-12-31 23:59:59', '演示CDK - 20次机会', 'admin');

-- 创建游戏类型记录（如果不存在）
INSERT IGNORE INTO game_types (id, game_code, game_name, description, version, is_active) 
VALUES (3, 'LUCKY_WHEEL', '小里幸运转盘', '基于CDK的转盘抽奖游戏', '1.0.0', TRUE);

COMMIT;
