package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "lucky_wheel_cdk",
       indexes = {
           @Index(name = "idx_cdk_code", columnList = "cdk_code", unique = true),
           @Index(name = "idx_cdk_status", columnList = "status"),
           @Index(name = "idx_cdk_created", columnList = "created_at")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyWheelCDK {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "cdk_code", nullable = false, unique = true, length = 50)
    private String cdkCode;
    
    @Column(name = "max_spins", nullable = false)
    private Integer maxSpins;
    
    @Column(name = "used_spins")
    private Integer usedSpins = 0;
    
    @Column(name = "status", length = 20)
    private String status = "ACTIVE"; // ACTIVE, EXPIRED, DISABLED
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Column(name = "description", length = 200)
    private String description;
    
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "first_used_at")
    private LocalDateTime firstUsedAt;
    
    @Column(name = "last_used_at")
    private LocalDateTime lastUsedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查CDK是否可用
     */
    public boolean isAvailable() {
        if (!"ACTIVE".equals(status)) {
            return false;
        }
        if (expireTime != null && LocalDateTime.now().isAfter(expireTime)) {
            return false;
        }
        return getRemainingSpins() > 0;
    }
    
    /**
     * 获取剩余次数
     */
    public int getRemainingSpins() {
        return maxSpins - usedSpins;
    }
    
    /**
     * 使用一次
     */
    public void useOnce() {
        if (isAvailable()) {
            usedSpins++;
            lastUsedAt = LocalDateTime.now();
            if (firstUsedAt == null) {
                firstUsedAt = LocalDateTime.now();
            }
        }
    }
    
    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
}
