package com.game.lianliankan.repository;

import com.game.lianliankan.entity.LuckyWheelAdmin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LuckyWheelAdminRepository extends JpaRepository<LuckyWheelAdmin, Long> {
    
    /**
     * 根据用户名查找管理员
     */
    Optional<LuckyWheelAdmin> findByUsername(String username);
    
    /**
     * 查找所有激活的管理员
     */
    List<LuckyWheelAdmin> findByStatusOrderByCreatedAtDesc(String status);
    
    /**
     * 查找所有管理员，按创建时间倒序
     */
    List<LuckyWheelAdmin> findAllByOrderByCreatedAtDesc();
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 统计激活的管理员数量
     */
    long countByStatus(String status);
}
